# 监控网站配置
monitors:
  amazon-popmart-it:
    enabled: true
    name: "W • AMAZON POPMART IT"
    url: "https://www.amazon.it"
    check_interval: 10 # 秒
    max_concurrency: 5
    currency: EUR
    spider_type: "amazon-wishlist"
    proxies: ["reverse_proxies.cloudflare"] # 使用代理组
    notifications: ["amazon-popmart-it"] # 使用通知组
    cookies: ['amazon-it-wishlist'] # 不使用Cookie
    merchantID: APLYZSTHRMK0Z
    wishlist:
      - 32LX6AFUQ3TZR
      - 13BCKN1D0PAWF
      - 33ODKCHLOR9CS
      - 1F8P13MAUPMNC

  amazon-popmart-de:
    enabled: true
    name: "W • AMAZON POPMART DE"
    url: "https://www.amazon.de"
    check_interval: 10 # 秒
    max_concurrency: 5
    currency: EUR
    spider_type: "amazon-wishlist"
    proxies: ["reverse_proxies.cloudflare"] # 使用代理组
    notifications: ["amazon-popmart-de"] # 使用通知组
    cookies: ['amazon-de-wishlist'] # 不使用Cookie
    merchantID: APLYZSTHRMK0Z
    wishlist:
      - MKR1S8AUQ4YU
      - 2C851KUQDY5ZA
      - 1TL8CF1XDG4JH
      - 317YK0RWB1ZQA

  amazon-popmart-es:
    enabled: true
    name: "W • AMAZON POPMART ES"
    url: "https://www.amazon.es"
    check_interval: 10 # 秒
    max_concurrency: 5
    currency: EUR
    spider_type: "amazon-wishlist"
    proxies: ["reverse_proxies.cloudflare"] # 使用代理组
    notifications: ["amazon-popmart-es"] # 使用通知组
    cookies: ['amazon-es-wishlist'] # 不使用Cookie
    merchantID: APLYZSTHRMK0Z
    wishlist:
      - 11J7TY5CHLTJH
      - 1F65DAI0XNYWA
      - REUVZ8J3XNUT
      - 2MWOCY8J2UITE

  amazon-popmart-fr:
    enabled: true
    name: "W • AMAZON POPMART FR"
    url: "https://www.amazon.fr"
    check_interval: 10 # 秒
    max_concurrency: 5
    currency: EUR
    spider_type: "amazon-wishlist"
    proxies: ["reverse_proxies.cloudflare"] # 使用代理组
    notifications: ["amazon-popmart-fr"] # 使用通知组
    cookies: ['amazon-fr-wishlist'] # 不使用Cookie
    merchantID: APLYZSTHRMK0Z
    wishlist:
      - 30PA6PR4KART4
      - 30ZCYF4E3D9ME
      - 11333YKSQ9KE4
      - 2QIOFVD1AG2LI

  amazon-popmart-gb:
    enabled: true
    name: "W • AMAZON POPMART GB"
    url: "https://www.amazon.co.uk"
    check_interval: 10 # 秒
    max_concurrency: 5
    currency: EUR
    spider_type: "amazon-wishlist"
    proxies: ["reverse_proxies.cloudflare"] # 使用代理组
    notifications: ["amazon-popmart-gb"] # 使用通知组
    cookies: ['amazon-gb-wishlist'] # 不使用Cookie
    merchantID: APLYZSTHRMK0Z
    wishlist:
      - 38L9X3MKMVZB8
      - 87MWGC1U3XTH
      - E6OKS28A06VE
      - 38HBREPJCX5W1

  # amazon-popmart-it-single:
  #   enabled: true
  #   name: "AMAZON POPMART IT"
  #   url: "https://www.amazon.it"
  #   check_interval: 30 # 秒
  #   max_concurrency: 20
  #   currency: EUR
  #   spider_type: "amazon"
  #   proxies: ["it"] # 使用代理组
  #   notifications: ["amazon-popmart-it"] # 使用通知组
  #   cookies: ['amazon-it'] # 不使用Cookie
  #   merchantID: APLYZSTHRMK0Z
  #   # 批量处理配置 - Amazon爬虫支持批量处理多个ASIN
  #   batch_processing:
  #     enabled: false        # 启用批量处理
  #     batch_size: 1       # 每批处理30个ASIN（可选，默认使用爬虫的最大值50）
  #     strategy: "single"  # 处理策略：optimal(自动选择), single(强制单个), batch(强制批量)
  #   asinlist:
  #     - B0CLNYKFYT
  #     - B0CLNYKLBW
  #     - B0CLNZGRDD
  #     - B0CLNZLZ5P
  #     - B0D7GG48FR
  #     - B0D7GG6WMM
  #     - B0D7H3M66R
  #     - B0D7Z931MG
  #     - B0DT41V371
  #     - B0DT44TSM2
  #     - B0DWM9JVPK
  #     - B0DWMD1SLX
  #     - B0DWMF7LB7
  #     - B0DWMFRMM7
  #     - B0CQYG9RVH
  #     - B0D1C9MRKS
  #     - B0D1C9VBMZ
  #     - B0D7P7SV6K
  #     - B0D7P7YYWY
  #     - B0DCDV7SL9
  #     - B0DFCDPG7L
  #     - B0DJY3ZKNF
  #     - B0DQSGQQVL
  #     - B0DQTKL1TQ
  #     - B0DQSMZJ9Q
  #     - B0DQTL945Q
  #     - B0DM7W93SQ
  #     - B0DM7WBHDW
  #     - B0DTKFD4S9
  #     - B0F6L8DWG3
  #     - B0F6LBQQ16
  #     - B0DPKFTQWW
  #     - B0DQTP3YLS
  #     - B0F7QCPYL3
  #     - B0F7R1ZYFN
  #     - B0F9XW8NCT
  #     - B0F9XWNMQG
  #     - B0F9F7RF3F
  #     - B0DJXVHQGP
  #     - B0DJY46B3N
  #     - B0F83FQ4J8
  #     - B0F8331G2S

  # amazon-popmart-de-single:
  #   enabled: true
  #   name: "AMAZON POPMART DE"
  #   url: "https://www.amazon.de"
  #   check_interval: 30 # 秒
  #   max_concurrency: 5
  #   currency: EUR
  #   spider_type: "amazon"
  #   proxies: ["de"] # 使用代理组
  #   notifications: ["amazon-popmart-de"] # 使用通知组
  #   cookies: ['amazon-de'] # 不使用Cookie
  #   merchantID: APLYZSTHRMK0Z 
  #   # 批量处理配置 - Amazon爬虫支持批量处理多个ASIN
  #   batch_processing:
  #     enabled: false        # 启用批量处理
  #     batch_size: 1       # 每批处理30个ASIN（可选，默认使用爬虫的最大值50）
  #     strategy: "single"  # 处理策略：optimal(自动选择), single(强制单个), batch(强制批量)
  #   asinlist:
  #     - B0CLNYKFYT
  #     - B0CLNYKLBW
  #     - B0CLNZGRDD
  #     - B0CLNZLZ5P
  #     - B0D7GG48FR
  #     - B0D7GG6WMM
  #     - B0D7H3M66R
  #     - B0D7Z931MG
  #     - B0DT41V371
  #     - B0DT44TSM2
  #     - B0DWM9JVPK
  #     - B0DWMD1SLX
  #     - B0DWMF7LB7
  #     - B0DWMFRMM7
  #     - B0CQYG9RVH
  #     - B0D1C9MRKS
  #     - B0D1C9VBMZ
  #     - B0D7P7SV6K
  #     - B0D7P7YYWY
  #     - B0DCDV7SL9
  #     - B0DFCDPG7L
  #     - B0DJY3ZKNF
  #     - B0DQSGQQVL
  #     - B0DQTKL1TQ
  #     - B0DQSMZJ9Q
  #     - B0DQTL945Q
  #     - B0DM7W93SQ
  #     - B0DM7WBHDW
  #     - B0DTKFD4S9
  #     - B0F6L8DWG3
  #     - B0F6LBQQ16
  #     - B0DPKFTQWW
  #     - B0DQTP3YLS
  #     - B0F7QCPYL3
  #     - B0F7R1ZYFN
  #     - B0DNJ7V72T
  #     - B0D815JQ19
  #     - B0F9XW8NCT
  #     - B0F9XWNMQG
  #     - B0F9F7RF3F
  #     - B0DJXVHQGP
  #     - B0DJY46B3N
  #     - B0F83FQ4J8
  #     - B0F8331G2S

  # amazon-popmart-es-single:
  #   enabled: true
  #   name: "AMAZON POPMART ES"
  #   url: "https://www.amazon.es"
  #   check_interval: 30 # 秒
  #   max_concurrency: 20
  #   currency: EUR
  #   spider_type: "amazon"
  #   proxies: ["es"] # 使用代理组
  #   notifications: ["amazon-popmart-es"] # 使用通知组
  #   cookies: ['amazon-es'] # 不使用Cookie
  #   merchantID: APLYZSTHRMK0Z 
  #   # 批量处理配置 - Amazon爬虫支持批量处理多个ASIN
  #   batch_processing:
  #     enabled: false        # 启用批量处理
  #     batch_size: 1       # 每批处理30个ASIN（可选，默认使用爬虫的最大值50）
  #     strategy: "single"  # 处理策略：optimal(自动选择), single(强制单个), batch(强制批量)
  #   asinlist:
  #     - B0CLNYKFYT
  #     - B0CLNYKLBW
  #     - B0CLNZGRDD
  #     - B0CLNZLZ5P
  #     - B0D7GG48FR
  #     - B0D7GG6WMM
  #     - B0D7H3M66R
  #     - B0D7Z931MG
  #     - B0DT41V371
  #     - B0DT44TSM2
  #     - B0DWM9JVPK
  #     - B0DWMD1SLX
  #     - B0DWMF7LB7
  #     - B0DWMFRMM7
  #     - B0CQYG9RVH
  #     - B0D1C9MRKS
  #     - B0D1C9VBMZ
  #     - B0D7P7SV6K
  #     - B0D7P7YYWY
  #     - B0DCDV7SL9
  #     - B0DFCDPG7L
  #     - B0DJY3ZKNF
  #     - B0DQSGQQVL
  #     - B0DQTKL1TQ
  #     - B0DQSMZJ9Q
  #     - B0DQTL945Q
  #     - B0DM7W93SQ
  #     - B0DM7WBHDW
  #     - B0DTKFD4S9
  #     - B0F6L8DWG3
  #     - B0F6LBQQ16
  #     - B0DPKFTQWW
  #     - B0DQTP3YLS
  #     - B0F7QCPYL3
  #     - B0F7R1ZYFN
  #     - B0F9XW8NCT
  #     - B0F9XWNMQG
  #     - B0F9F7RF3F
  #     - B0DJXVHQGP
  #     - B0DJY46B3N
  #     - B0F83FQ4J8
  #     - B0F8331G2S

  # amazon-popmart-fr-single:
  #   enabled: true
  #   name: "AMAZON POPMART FR"
  #   url: "https://www.amazon.fr"
  #   check_interval: 30 # 秒
  #   max_concurrency: 5
  #   currency: EUR
  #   spider_type: "amazon"
  #   proxies: ["fr"] # 使用代理组
  #   notifications: ["amazon-popmart-fr"] # 使用通知组
  #   cookies: ['amazon-fr'] # 不使用Cookie
  #   merchantID: APLYZSTHRMK0Z 
  #   # 批量处理配置 - Amazon爬虫支持批量处理多个ASIN
  #   batch_processing:
  #     enabled: false        # 启用批量处理
  #     batch_size: 1       # 每批处理30个ASIN（可选，默认使用爬虫的最大值50）
  #     strategy: "single"  # 处理策略：optimal(自动选择), single(强制单个), batch(强制批量)
  #   asinlist:
  #     - B0CLNYKFYT
  #     - B0CLNYKLBW
  #     - B0CLNZGRDD
  #     - B0CLNZLZ5P
  #     - B0D7GG48FR
  #     - B0D7GG6WMM
  #     - B0D7H3M66R
  #     - B0D7Z931MG
  #     - B0DT41V371
  #     - B0DT44TSM2
  #     - B0DWM9JVPK
  #     - B0DWMD1SLX
  #     - B0DWMF7LB7
  #     - B0DWMFRMM7
  #     - B0CQYG9RVH
  #     - B0D1C9MRKS
  #     - B0D1C9VBMZ
  #     - B0D7P7SV6K
  #     - B0D7P7YYWY
  #     - B0DCDV7SL9
  #     - B0DFCDPG7L
  #     - B0DJY3ZKNF
  #     - B0DQSGQQVL
  #     - B0DQTKL1TQ
  #     - B0DQSMZJ9Q
  #     - B0DQTL945Q
  #     - B0DM7W93SQ
  #     - B0DM7WBHDW
  #     - B0DTKFD4S9
  #     - B0F6L8DWG3
  #     - B0F6LBQQ16
  #     - B0DPKFTQWW
  #     - B0DQTP3YLS
  #     - B0F7QCPYL3
  #     - B0F7R1ZYFN
  #     - B0CQXJ4J6Y
  #     - B0F9XW8NCT
  #     - B0F9XWNMQG
  #     - B0F9F7RF3F
  #     - B0DJXVHQGP
  #     - B0DJY46B3N
  #     - B0F83FQ4J8
  #     - B0F8331G2S

  # amazon-popmart-gb-single:
  #   enabled: true
  #   name: "AMAZON POPMART GB"
  #   url: "https://www.amazon.co.uk"
  #   check_interval: 30 # 秒
  #   max_concurrency: 20
  #   currency: GBP
  #   spider_type: "amazon"
  #   proxies: ["gb"] # 使用代理组
  #   notifications: ["amazon-popmart-gb"] # 使用通知组
  #   cookies: ['amazon-gb'] # 使用amazon-gb cookie组
  #   merchantID: APLYZSTHRMK0Z 
  #   # 批量处理配置 - Amazon爬虫支持批量处理多个ASIN
  #   batch_processing:
  #     enabled: false        # 启用批量处理
  #     batch_size: 1       # 每批处理30个ASIN（可选，默认使用爬虫的最大值50）
  #     strategy: "single"  # 处理策略：optimal(自动选择), single(强制单个), batch(强制批量)
  #   asinlist:
  #     - B0D7H3M66R
  #     - B0D7Z931MG
  #     - B0DT41V371
  #     - B0DT44TSM2
  #     - B0DWM9JVPK
  #     - B0DWMD1SLX
  #     - B0DWMF7LB7
  #     - B0DWMFRMM7
  #     - B0CQYG9RVH
  #     - B0D1C9MRKS
  #     - B0D1C9VBMZ
  #     - B0D7P7YYWY
  #     - B0DCDV7SL9
  #     - B0DFCDPG7L
  #     - B0DJY3ZKNF
  #     - B0DQSGQQVL
  #     - B0DQTL945Q
  #     - B0DTKFD4S9
  #     - B0F6L8DWG3
  #     - B0DPKFTQWW
  #     - B0DQTP3YLS
  #     - B0D815JQ19
  #     - B0CQXJ4J6Y
  #     - B0CMXFNC9M
  #     - B0D1TJRY14
  #     - B0D1TR6BGT
  #     - B0DCZ2HK9R
  #     - B0DGX39Y1Q
  #     - B0DGX4CDL3
  #     - B0DJWFYMCY
  #     - B0DJWHK44C
  #     - B0DQSPDFPH
  #     - B0DJWKBL3H
  #     - B0F7QPMFN7
  #     - B0DM6C2QN8
  #     - B0DM6GLTNB
  #     - B0DTKL37P4
  #     - B0F6MNF2XX
  #     - B0F6MPJVWH # 丑鱼 端

  # amazon-popmart-jp-single:
  #   enabled: true
  #   name: "AMAZON POPMART JP"
  #   url: "https://www.amazon.co.jp"
  #   check_interval: 30 # 秒
  #   max_concurrency: 20
  #   currency: JPY
  #   spider_type: "amazon"
  #   proxies: ["jp"] # 使用代理组
  #   notifications: ["amazon-popmart-jp"] # 使用通知组
  #   cookies: ['amazon-jp'] # 使用amazon-gb cookie组
  #   merchantID: A2NY84BTEAWXLG 
  #   # 批量处理配置 - Amazon爬虫支持批量处理多个ASIN
  #   batch_processing:
  #     enabled: false        # 启用批量处理
  #     batch_size: 1       # 每批处理30个ASIN（可选，默认使用爬虫的最大值50）
  #     strategy: "single"  # 处理策略：optimal(自动选择), single(强制单个), batch(强制批量)
  #   asinlist:
  #     - B0DDWVMNSS  # 小米兰
  #     - B0CYQ5SZCC  # 小春野
  #     - B0DPLZL4GQ  # 皇后
  #     - B0CL33T8X6  # 1.0 马卡龙 6 盒
  #     - B0CL6Q2BDG  # 1.0 马卡龙 1 盒
  #     - B0D8TGLR26  # 2.0 坐坐 3 盒
  #     - B01MRR8IJC  # 3.0 1 盒
  #     - B0DZ6QLR7S  # 3.0 1 盒
  #     - B0DZ6N57RW  # 3.0 3 盒
  #     - B0DZ6PG39L  # 3.0 6 盒
  #     - B0DXXXC89J  # 可乐
  #     - B0DQ176P7F  # 可乐 3 盒
  #     - B0DFLL726M  # 大天使
  #     - B0CYQ6QCHD  # 大春野
  #     - B0DPLZ4QR5  # 国王
  #     - B0D7M2D97T  # 大夏

  popmart-eu:
    enabled: true
    name: "POP MART EU"
    url: "https://www.popmart.com/it"
    api: 'https://prod-intl-api.popmart.com'
    country: "IT"
    check_interval: 20 # 秒
    max_concurrency: 5
    currency: "EUR"
    spider_type: "popmart"
    proxies: ["isp"] # 使用代理组
    notifications: ["popmart-it", "popmart-de", "popmart-es", "popmart-fr", "popmart-nl", "popmart-lu"] # 使用通知组
    cookies: [] # 不使用Cookie
    fingerprint: popmart-eu
    filter: 1380202482168238120
    collections:
      collection-33:  # 集合ID作为键
        collectionId: 33
        brandIDs: [1, 4, 8, 15, 18, 23, 32]
        categoryIDs: [101, 110, 109, 100, 116, 98, 103, 108]
        sortWay: 1
        pageSize: 1000

  popmart-gb:
    enabled: true
    name: "POP MART GB"
    url: "https://www.popmart.com/gb"
    api: 'https://prod-intl-api.popmart.com'
    country: "GB"
    check_interval: 20 # 秒
    max_concurrency: 5
    currency: "GBP"
    spider_type: "popmart"
    proxies: ["isp"] # 使用代理组
    notifications: ["popmart-gb"] # 使用通知组
    cookies: [] # 不使用Cookie
    fingerprint: popmart-gb
    filter: 1380205537656045628
    collections:
      collection-33:  # 集合ID作为键
        collectionId: 33
        brandIDs: [3, 5, 17, 18, 21, 30, 31, 85]
        categoryIDs: [101, 110, 109, 100, 103, 116, 98, 108]
        sortWay: 1
        pageSize: 1000
      

  popmart-au:
    enabled: true
    name: "POP MART AU"
    url: "https://www.popmart.com/au"
    api: 'https://prod-intl-api.popmart.com'
    country: "AU"
    check_interval: 20 # 秒
    max_concurrency: 5
    currency: "AUD"
    spider_type: "popmart"
    proxies: ["isp"] # 使用代理组
    notifications: ["popmart-au"] # 使用通知组
    cookies: [] # 不使用Cookie
    fingerprint: popmart-au
    filter: 1380205569801195620
    collections:
      collection-33:  # 集合ID作为键
        collectionId: 33
        brandIDs: [1, 4, 8, 15, 18, 23, 32]
        categoryIDs: [109, 100, 116, 108, 98, 103]
        sortWay: 1
        pageSize: 1000

  popmart-sg:
    enabled: true
    name: "POP MART SG"
    url: "https://www.popmart.com/sg"
    api: 'https://prod-intl-api.popmart.com'
    country: "SG"
    check_interval: 20 # 秒
    max_concurrency: 5
    currency: "SGD"
    spider_type: "popmart"
    proxies: ["isp"] # 使用代理组
    notifications: ["popmart-sg"] # 使用通知组
    cookies: [] # 不使用Cookie
    fingerprint: popmart-sg
    filter: 1380205595193245778
    collections:
      collection-33:  # 集合ID作为键
        collectionId: 33
        brandIDs: [1, 4, 8, 15, 18, 23, 32]
        categoryIDs: [101, 100, 110, 109, 116, 108, 98, 103]
        sortWay: 1
        pageSize: 1000

  popmart-th:
    enabled: true
    name: "POP MART TH"
    url: "https://www.popmart.com/th"
    api: 'https://prod-asia-api.popmart.com'
    country: "TH"
    check_interval: 20 # 秒
    max_concurrency: 5
    currency: "THD"
    spider_type: "popmart"
    proxies: ["isp"] # 使用代理组
    notifications: ["popmart-th"] # 使用通知组
    cookies: [] # 不使用Cookie
    fingerprint: popmart-th
    filter: 1380205619880919040
    collections:
      collection-33:  # 集合ID作为键
        collectionId: 33
        brandIDs: [1, 8, 18, 23, 32, 89]
        categoryIDs: [120, 104, 109, 108, 100, 116, 111, 103, 98]
        sortWay: 1
        pageSize: 1000

  # popmart-us:
  #   enabled: true
  #   name: "POP MART US"
  #   url: "https://www.popmart.com/us"
  #   api: 'https://prod-na-api.popmart.com'
  #   country: "US"
  #   check_interval: 20 # 秒
  #   max_concurrency: 5
  #   currency: "USD"
  #   spider_type: "popmart"
  #   proxies: ["isp"] # 使用代理组
  #   notifications: ["popmart-us"] # 使用通知组
  #   cookies: [] # 不使用Cookie
  #   fingerprint: popmart-us
  #   collections:
  #     collection-1:  # 集合ID作为键
  #       collectionId: 1
  #       brandIDs: [3, 5, 9, 15, 21, 30, 31, 40]
  #       categoryIDs: [85, 118, 71, 72, 70, 73, 74]
  #       sortWay: 1
  #       pageSize: 1000

  popmart-hk:
    enabled: true
    name: "POP MART HK"
    url: "https://www.popmart.com/hk"
    api: 'https://prod-hk-api.popmart.com'
    country: "HK"
    check_interval: 20 # 秒
    max_concurrency: 5
    currency: "HKD"
    spider_type: "popmart"
    proxies: ["isp"] # 使用代理组
    notifications: ["popmart-hk"] # 使用通知组
    cookies: [] # 不使用Cookie
    fingerprint: popmart-hk
    filter: 1396024418542686229
    collections:
      collection-180:  # 集合ID作为键
        collectionId: 180
        brandIDs: [1, 4, 8, 15, 18, 23, 32, 89]
        categoryIDs: [98, 101, 104, 110, 108, 113, 116]
        sortWay: 1
        pageSize: 1000

  popmart-kr:
    enabled: true
    name: "POP MART KR"
    url: "https://www.popmart.com/kr"
    api: 'https://prod-intl-api.popmart.com'
    country: "KR"
    check_interval: 20 # 秒
    max_concurrency: 5
    currency: "KRW"
    spider_type: "popmart"
    proxies: ["isp"] # 使用代理组
    notifications: ["popmart-kr"] # 使用通知组
    cookies: [] # 不使用Cookie
    fingerprint: popmart-kr
    filter: 1396024380378710066
    collections:
      collection-33:  # 集合ID作为键
        collectionId: 33
        brandIDs: [1, 4, 8, 15, 18, 23, 32]
        categoryIDs: [101, 110, 109, 100, 103, 116, 98, 108]
        sortWay: 1
        pageSize: 1000
        
  popmart-jp:
    enabled: true
    name: "POP MART JP"
    url: "https://www.popmart.com/jp"
    api: 'https://prod-intl-api.popmart.com'
    country: "JP"
    check_interval: 20 # 秒
    max_concurrency: 5
    currency: "JPY"
    spider_type: "popmart"
    proxies: ["isp"] # 使用代理组
    notifications: ["popmart-jp"] # 使用通知组
    cookies: [] # 不使用Cookie
    fingerprint: popmart-jp
    filter: 1396024346627276843
    collections:
      collection-244:  # 集合ID作为键
        collectionId: 244
        brandIDs: [1, 4, 8, 15, 18, 23, 32]
        categoryIDs: [101, 110, 109, 103, 108, 116, 100, 98]
        sortWay: 1
        pageSize: 1000
    
  popmart-my:
    enabled: true
    name: "POP MART MY"
    url: "https://www.popmart.com/my"
    api: 'https://prod-intl-api.popmart.com'
    country: "MY"
    check_interval: 20 # 秒
    max_concurrency: 5
    currency: "RM"
    spider_type: "popmart"
    proxies: ["isp"] # 使用代理组
    notifications: ["popmart-my"] # 使用通知组
    cookies: [] # 不使用Cookie
    fingerprint: popmart-my
    filter: 1398425056488718336
    collections:
      collection-33:  # 集合ID作为键
        collectionId: 33
        brandIDs: [1, 4, 8, 15, 18, 23, 32]
        categoryIDs: [101, 110, 109, 108, 100, 116, 98, 103]
        sortWay: 1
        pageSize: 1000

  # aliexpress-popmart-all:
  #   enabled: true
  #   name: 'ALIEXPRESS ALL'
  #   url: 'https://www.aliexpress.com'
  #   country: 'IT'
  #   check_interval: 20  # 秒
  #   max_concurrency: 5
  #   currency: USD
  #   spider_type: "aliexpress"
  #   proxies: ["default"] # 使用代理组 # 使用代理组
  #   notifications: ["aliexpress-it", "aliexpress-de", "aliexpress-es", "aliexpress-fr", "aliexpress-gb", "aliexpress-au", "aliexpress-sg", "aliexpress-th", "aliexpress-jp"] # 使用通知组
  #   cookies: ["aliexpress-all"] # 不使用Cookie
  #   product_ids: 
  #     - 1005006169948468
  #     - 1005007350029637
  #     - 1005007992059512
  #     - 1005008089727485
  #     - 1005008411924664
  #     - 1005008683037500
  #     - 1005008878463323
  #     - 1005008883166246
  #     - 1005008883361345
  #     - 1005008886331473
  #     - 1005008886796363
  #     - 1005009031131478
  #     - 1005009239929692
  #     - 1005009239969181
  #     - 1005009306008707
  #     - 1005009306425220
  #     - 1005009352765819
  #     - 1005009553270684

  # aliexpress-popmart-EU:
  #   enabled: true
  #   name: 'ALIEXPRESS EU'
  #   url: 'https://www.aliexpress.com'
  #   country: 'IT'
  #   check_interval: 20  # 秒
  #   max_concurrency: 5
  #   currency: USD
  #   spider_type: "aliexpress"
  #   proxies: ["default"] # 使用代理组 # 使用代理组
  #   notifications: ["aliexpress-it", "aliexpress-de", "aliexpress-es", "aliexpress-fr"] # 使用通知组
  #   cookies: ["aliexpress-eu"] # 不使用Cookie
  #   product_ids: 
  #     - 1005008116699994
  #     - 1005008117007093
  #     - 1005008117198847
  #     - 1005008120783891
  #     - 1005008162983993
  #     - 1005008411977215
  #     - 1005008455663024
  #     - 1005008457416289
  #     - 1005008543878340
  #     - 1005008563299866
  #     - 1005008603257633
  #     - 1005008603326708
  #     - 1005008603490144
  #     - 1005008886096545
  #     - 1005009046817390
  #     - 1005009187115882

  # aliexpress-popmart-de+fr+ne:
  #   enabled: true
  #   name: 'ALIEXPRESS DE+FR+NE'
  #   url: 'https://www.aliexpress.com'
  #   country: 'DE'
  #   check_interval: 20 # 秒
  #   max_concurrency: 5
  #   currency: USD
  #   spider_type: "aliexpress"
  #   proxies: ["default"] # 使用代理组 # 使用代理组
  #   notifications: ["aliexpress-de", "aliexpress-fr"] # 使用通知组
  #   cookies: ["aliexpress-de"] # 不使用Cookie
  #   product_ids: 
  #     - 1005009344479726

  # aliexpress-popmart-it:
  #   enabled: true
  #   name: 'ALIEXPRESS IT'
  #   url: 'https://www.aliexpress.com'
  #   country: 'IT'
  #   check_interval: 20 # 秒
  #   max_concurrency: 5
  #   currency: USD
  #   spider_type: "aliexpress"
  #   proxies: ["default"] # 使用代理组 # 使用代理组
  #   notifications: ["aliexpress-it"] # 使用通知组
  #   cookies: ["aliexpress-it"] # 不使用Cookie
  #   product_ids: 
  #     - 1005007989903747
  #     - 1005008885307271
  #     - 1005008979857770
  #     - 1005009185208716
  #     - 1005009185208716
  #     - 1005009288695870
  #     - 1005009340543994

  # aliexpress-popmart-es:
  #   enabled: true
  #   name: 'ALIEXPRESS ES'
  #   url: 'https://www.aliexpress.com'
  #   country: 'ES'
  #   check_interval: 20  # 秒
  #   max_concurrency: 5
  #   currency: USD
  #   spider_type: "aliexpress"
  #   proxies: ["default"] # 使用代理组 # 使用代理组
  #   notifications: ["aliexpress-es"] # 使用通知组
  #   cookies: ["aliexpress-es"] # 不使用Cookie
  #   product_ids: 
  #     - 1005008666002504
  #     - 1005008886163127
  #     - 1005009176599514
  #     - 1005009289151592
  #     - 1005009289198567
  #     - 1005009344331951
  #     - 1005009344603265
  
  # aliexpress-popmart-gb:
  #   enabled: true
  #   name: 'ALIEXPRESS GB'
  #   url: 'https://www.aliexpress.com'
  #   country: 'GB'
  #   check_interval: 20  # 秒
  #   max_concurrency: 5
  #   currency: USD
  #   spider_type: "aliexpress"
  #   proxies: ["default"] # 使用代理组 # 使用代理组
  #   notifications: ["aliexpress-gb"] # 使用通知组
  #   cookies: ["aliexpress-gb"] # 不使用Cookie
  #   product_ids: 
  #     - 1005007966229736
  #     - 1005007966773739
  #     - 1005007967085112
  #     - 1005008270485561
  #     - 1005008450990135
  #     - 1005008455641110
  #     - 1005008563458441
  #     - 1005008883462026
  #     - 1005009340400981

  # aliexpress-popmart-au:
  #   enabled: true
  #   name: 'ALIEXPRESS AU'
  #   url: 'https://www.aliexpress.com'
  #   country: 'AU'
  #   check_interval: 20  # 秒
  #   max_concurrency: 5
  #   currency: USD
  #   spider_type: "aliexpress"
  #   proxies: ["default"] # 使用代理组 # 使用代理组
  #   notifications: ["aliexpress-au"] # 使用通知组
  #   cookies: ["aliexpress-au"] # 不使用Cookie
  #   product_ids: 
  #     - 1005004895736299
  #     - 1005008070817993
  #     - 1005008077707827
  #     - 1005008270486581
  #     - 1005008652722283
  #     - 1005008652762179
  #     - 1005008652785302
  #     - 1005008836472526
  #     - 1005008886083825
  #     - 1005009279484167

  # aliexpress-popmart-sg:
  #   enabled: true
  #   name: 'ALIEXPRESS SG'
  #   url: 'https://www.aliexpress.com'
  #   country: 'SG'
  #   check_interval: 20  # 秒
  #   max_concurrency: 5
  #   currency: USD
  #   spider_type: "aliexpress"
  #   proxies: ["default"] # 使用代理组 # 使用代理组
  #   notifications: ["aliexpress-sg"] # 使用通知组
  #   cookies: ["aliexpress-sg"] # 不使用Cookie
  #   product_ids: 
  #     - 1005007198494636

  # aliexpress-popmart-th:
  #   enabled: true
  #   name: 'ALIEXPRESS TH'
  #   url: 'https://www.aliexpress.com'
  #   country: 'TH'
  #   check_interval: 20  # 秒
  #   max_concurrency: 5
  #   currency: USD
  #   spider_type: "aliexpress"
  #   proxies: ["default"] # 使用代理组 # 使用代理组
  #   notifications: ["aliexpress-th"] # 使用通知组
  #   cookies: ["aliexpress-th"] # 不使用Cookie
  #   product_ids: 
  #     - 1005008270797343

  # aliexpress-popmart-jp:
  #   enabled: true
  #   name: 'ALIEXPRESS JP'
  #   url: 'https://www.aliexpress.com'
  #   country: 'JP'
  #   check_interval: 20  # 秒
  #   max_concurrency: 5
  #   currency: USD
  #   spider_type: "aliexpress"
  #   proxies: ["default"] # 使用代理组 # 使用代理组
  #   notifications: ["aliexpress-jp"] # 使用通知组
  #   cookies: ["aliexpress-jp"] # 不使用Cookie
  #   product_ids: 
  #     - 1005008080552433
  #     - 1005008596249940
  #     - 1005008596632176