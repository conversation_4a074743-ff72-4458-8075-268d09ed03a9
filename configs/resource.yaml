# 资源初始化配置
resource:
  # 全局设置
  global:
    enabled: true
    refresh_interval: 300s  # 默认刷新间隔
    max_retry_attempts: 3
    retry_delay: 5s

  # 资源初始化器配置
  initializers:
    # Amazon zipCode资源初始化器
    amazon_zipcode_cookies:
      enabled: false
      priority: 16
      dependencies: []  # 不依赖其他资源
      ttl: 900s  # 30分钟
      config:
        regions:
          # it:
          #   zipCode: "50065"
          #   country: "IT"
          #   url: "https://www.amazon.it"
          #   cookies: ["amazon-it"]
          #   proxies: ["it"]
          #   ignoreCountry: true
          # de:
          #   zipCode: "70563"
          #   country: "DE"
          #   url: "https://www.amazon.de"
          #   cookies: ["amazon-de"]
          #   proxies: ["de"]
          #   ignoreCountry: true
          # es:
          #   zipCode: "08025"
          #   country: "ES"
          #   url: "https://www.amazon.es"
          #   cookies: ["amazon-es"]
          #   proxies: ["es"]
          #   ignoreCountry: true
          # fr:
          #   zipCode: "75011"
          #   country: "FR"
          #   url: "https://www.amazon.fr"
          #   cookies: ["amazon-fr"]
          #   proxies: ["fr"]
          #   ignoreCountry: true
          # gb:
          #   zipCode: "NW1 5LR"
          #   country: "GB"
          #   url: "https://www.amazon.co.uk"
          #   cookies: ["amazon-gb"]
          #   proxies: ["gb"]
          #   ignoreCountry: true
          # jp:
          #   zipCode: "100-0001"
          #   country: "JP"
          #   url: "https://www.amazon.co.jp"
          #   cookies: ["amazon-jp"]
          #   proxies: ["jp"]
          #   ignoreCountry: true
          it-wishlist:
            zipCode: "50065"
            country: "IT"
            url: "https://www.amazon.it"
            cookies: ["amazon-it-wishlist"]
            proxies: ["it"]
            ignoreCountry: true
          de-wishlist:
            zipCode: "70563"
            country: "DE"
            url: "https://www.amazon.de"
            cookies: ["amazon-de-wishlist"]
            proxies: ["de"]
            ignoreCountry: true
          es-wishlist:
            zipCode: "08025"
            country: "ES"
            url: "https://www.amazon.es"
            cookies: ["amazon-es-wishlist"]
            proxies: ["es"]
            ignoreCountry: true
          fr-wishlist:
            zipCode: "75011"
            country: "FR"
            url: "https://www.amazon.fr"
            cookies: ["amazon-fr-wishlist"]
            proxies: ["fr"]
            ignoreCountry: true
          gb-wishlist:
            zipCode: "NW1 5LR"
            country: "GB"
            url: "https://www.amazon.co.uk"
            cookies: ["amazon-gb-wishlist"]
            proxies: ["gb"]
            ignoreCountry: true
        # 请求配置
        request_config:
          timeout: "30s"
          max_retries: 3

    # Amazon ACP参数资源初始化器
    amazon_acp:
      enabled: false
      priority: 17
      dependencies: ["amazon_zipcode_cookies"]  # 依赖zipcode初始化器
      ttl: 1800s  # 30分钟
      config:
        regions:
          it:
            url: "https://www.amazon.it"
            cookies: ["amazon-it"]
            proxies: ["it"]
          de:
            url: "https://www.amazon.de"
            cookies: ["amazon-de"]
            proxies: ["de"]
          es:
            url: "https://www.amazon.es"
            cookies: ["amazon-es"]
            proxies: ["es"]
          fr:
            url: "https://www.amazon.fr"
            cookies: ["amazon-fr"]
            proxies: ["fr"]
          gb:
            url: "https://www.amazon.co.uk"
            cookies: ["amazon-gb"]
            proxies: ["gb"]
        # 请求配置
        request_config:
          fallback_url: "/hz/rhf?currentPageType=Detail&currentSubPageType=Glance&excludeAsin=B00499YY50&fieldKeywords=&k=&keywords=&search=&auditEnabled=&previewCampaigns=&forceWidgets=&searchAlias=&isAUI=1&cardJSPresent=true&pageUrl=%2Fdp%2FB00499YY50"
          timeout: "30s"
          max_retries: 3
          default_headers:
            Accept: "*/*"
            Accept-Language: "zh-CN,zh;q=0.9"
            X-Requested-With: "XMLHttpRequest"
            Sec-Fetch-Site: "same-origin"
            Sec-Fetch-Mode: "cors"
            Sec-Fetch-Dest: "empty"
        # 解析配置
        parsing_config:
          acp_patterns:
            - 'data-acp-params="([^"]+)"'
            - 'data-acp-path="([^"]+)"'
            - 'tok=([^;]+);ts=([^;]+);rid=([^;]+);d1=([^;]+);d2=([^;]+);tpm=([^;]+);ref=([^;"]+)'
          acp_redis_key: "x-amz-acp-params"
          acp_path_redis_key: "x-amz-acp-path"

    # 远程配置初始化器
    remote_config:
      enabled: false
      priority: 10
      dependencies: []  # 不再依赖 Redis 初始化器
      ttl: 1800s  # 30分钟
      config:
        endpoints:
          - "https://config.example.com/api/v1/config"
          - "https://backup-config.example.com/api/v1/config"
        auth_token: "${CONFIG_AUTH_TOKEN}"
        cache_key: "remote_config"
        timeout: 30s
        
    # 远程 cookies 初始化器
    remote_cookies:
      enabled: false
      priority: 20
      dependencies: []  # 不再依赖 Redis 初始化器
      ttl: 3600s  # 1小时
      config:
        endpoints:
          amazon: "https://cookies.example.com/api/v1/cookies/amazon"
          aliexpress: "https://cookies.example.com/api/v1/cookies/aliexpress"
          popmart: "https://cookies.example.com/api/v1/cookies/popmart"
        auth_token: "${COOKIES_AUTH_TOKEN}"
        cache_prefix: "cookies:"
        timeout: 30s
        
    # 签名信息初始化器
    signature_info:
      enabled: false
      priority: 30
      dependencies: ["remote_config"]  # 只依赖远程配置
      ttl: 7200s  # 2小时
      config:
        endpoints:
          popmart: "https://api.example.com/signature/popmart"
          amazon: "https://api.example.com/signature/amazon"
        auth_token: "${SIGNATURE_AUTH_TOKEN}"
        cache_prefix: "signature:"
        timeout: 30s

# 环境变量说明
# CONFIG_AUTH_TOKEN: 远程配置服务的认证令牌
# COOKIES_AUTH_TOKEN: Cookies 服务的认证令牌  
# SIGNATURE_AUTH_TOKEN: 签名服务的认证令牌

# 配置说明：
# 1. priority: 数字越小优先级越高，Redis 为 1（最高优先级）
# 2. dependencies: 依赖的其他资源名称列表
# 3. ttl: 资源生存时间，0 表示永不过期
# 4. endpoints: 远程端点 URL，支持环境变量展开
# 5. cache_prefix: Redis 缓存键前缀
# 6. timeout: HTTP 请求超时时间

# 依赖关系图：
# Redis 由服务管理器直接管理，不在资源初始化器中
# remote_config (priority: 10, no dependencies)
# remote_cookies (priority: 20, no dependencies)
# signature_info (priority: 30, depends on: remote_config)

# 初始化顺序：
# 1. remote_config, remote_cookies (并行)
# 2. signature_info

# 刷新策略：
# - Redis: 由服务管理器直接管理，不需要刷新
# - remote_config: 每 30 分钟刷新一次
# - remote_cookies: 每 1 小时刷新一次
# - signature_info: 每 2 小时刷新一次

# 错误处理：
# - Redis 由服务管理器管理，如果初始化失败，整个系统无法启动
# - 如果远程资源初始化失败，会记录错误但不阻止系统启动
# - 刷新失败时会自动重试，重试次数由 max_retry_attempts 控制

# 缓存策略：
# - 所有远程资源都会缓存到 Redis 中（通过依赖注入的 Redis 管理器）
# - 缓存键格式：{key_prefix}resource:{resource_type}:{platform}
# - 例如：monitor:resource:cookies:amazon

# 监控和健康检查：
# - 每个资源都提供健康检查接口
# - 支持获取详细的状态信息和统计数据
# - 过期资源会自动触发刷新

# 开发和测试：
# - 可以通过设置 enabled: false 禁用特定的初始化器
# - 支持通过环境变量覆盖配置
# - 提供详细的日志记录用于调试
