package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"time"

	"go-monitor/internal/models"
	"go-monitor/internal/pipelines"
	"go-monitor/internal/services/redis"
)

// 演示过滤管道的使用
func main() {
	fmt.Println("=== 过滤管道演示 ===")

	// 1. 创建Redis管理器
	redisManager := redis.NewRedisManager(nil) // 使用默认配置
	if err := redisManager.Start(); err != nil {
		log.Printf("Redis连接失败（演示将使用模拟数据）: %v", err)
		// 在实际环境中，这里会连接到Redis
		// 为了演示，我们将模拟配置数据
		demonstrateWithoutRedis()
		return
	}
	defer redisManager.Stop()

	// 2. 创建过滤管道（现在使用动态过滤器参数）
	filterPipeline := pipelines.NewFilterPipeline(120, map[string]interface{}{})
	filterPipeline.SetRedisManager(redisManager)

	// 3. 打开管道
	if err := filterPipeline.Open(); err != nil {
		log.Fatalf("打开过滤管道失败: %v", err)
	}
	defer filterPipeline.Close()

	// 4. 设置演示配置到Redis
	setupDemoConfig(redisManager)

	// 5. 创建测试商品
	testProducts := createTestProducts()

	// 6. 处理商品并显示结果
	fmt.Println("\n=== 处理商品 ===")
	ctx := context.Background()

	for i, product := range testProducts {
		fmt.Printf("\n商品 %d: %s - %s\n", i+1, product.ProductID, product.Title)

		result, err := filterPipeline.ProcessItem(ctx, product)
		if err != nil {
			fmt.Printf("  ❌ 处理错误: %v\n", err)
			continue
		}

		if result == nil {
			fmt.Printf("  🚫 被过滤器拦截\n")
		} else {
			fmt.Printf("  ✅ 通过过滤器\n")
		}
	}

	// 7. 显示统计信息
	fmt.Println("\n=== 统计信息 ===")
	stats := filterPipeline.GetStats()
	for key, value := range stats {
		fmt.Printf("%s: %v\n", key, value)
	}
}

// 设置演示配置
func setupDemoConfig(redisManager *redis.RedisManager) {
	config := map[string]interface{}{
		"channel_info": map[string]interface{}{
			"channel_id":   "demo_channel_123",
			"channel_name": "演示频道",
			"guild_id":     "demo_guild_456",
			"created_at":   time.Now().Add(-time.Hour),
			"updated_at":   time.Now(),
		},
		"filter_rules": []map[string]interface{}{
			{
				"keyword":    "blocked",
				"mode":       "blacklist",
				"created_by": "demo_user",
			},
			{
				"keyword":    "spam",
				"mode":       "blacklist",
				"created_by": "demo_user",
			},
			{
				"keyword":    "premium",
				"mode":       "whitelist",
				"created_by": "demo_user",
			},
		},
	}

	configData, _ := json.Marshal(config)
	ctx := context.Background()

	// 设置到Redis
	key := "zeka:filter_rules:channel:demo_channel"
	if err := redisManager.Set(ctx, key, configData, 24*time.Hour); err != nil {
		log.Printf("设置演示配置失败: %v", err)
	} else {
		fmt.Println("✅ 演示配置已设置到Redis")
	}
}

// 创建测试商品
func createTestProducts() []*models.ProductItem {
	return []*models.ProductItem{
		{
			ProductID: "PROD001",
			Title:     "Premium Gaming Headset",
			URL:       "https://example.com/prod001",
			Platform:  "demo",
			Price:     99.99,
			Currency:  "USD",
			InStock:   true,
			Country:   "US",
			SiteURL:   "https://example.com",
			CrawledAt: time.Now(),
			Metadata: map[string]interface{}{
				"filter": "demo_channel", // 动态过滤器参数
			},
		},
		{
			ProductID: "PROD002",
			Title:     "Blocked Product Example",
			URL:       "https://example.com/prod002",
			Platform:  "demo",
			Price:     49.99,
			Currency:  "USD",
			InStock:   true,
			Country:   "US",
			SiteURL:   "https://example.com",
			CrawledAt: time.Now(),
			Metadata: map[string]interface{}{
				"filter": "demo_channel",
			},
		},
		{
			ProductID: "PROD003",
			Title:     "Spam Product Advertisement",
			URL:       "https://example.com/prod003",
			Platform:  "demo",
			Price:     19.99,
			Currency:  "USD",
			InStock:   false,
			Country:   "US",
			SiteURL:   "https://example.com",
			CrawledAt: time.Now(),
			Metadata: map[string]interface{}{
				"filter": "demo_channel",
			},
		},
		{
			ProductID: "PROD004",
			Title:     "Regular Product",
			URL:       "https://example.com/prod004",
			Platform:  "demo",
			Price:     79.99,
			Currency:  "USD",
			InStock:   true,
			Country:   "US",
			SiteURL:   "https://example.com",
			CrawledAt: time.Now(),
			Metadata: map[string]interface{}{
				"filter": "demo_channel",
			},
		},
		{
			ProductID: "PROD005",
			Title:     "Premium Quality Item",
			URL:       "https://example.com/prod005",
			Platform:  "demo",
			Price:     129.99,
			Currency:  "USD",
			InStock:   true,
			Country:   "US",
			SiteURL:   "https://example.com",
			CrawledAt: time.Now(),
			Metadata: map[string]interface{}{
				"filter": "demo_channel",
			},
		},
	}
}

// 无Redis环境的演示
func demonstrateWithoutRedis() {
	fmt.Println("\n=== 无Redis环境演示 ===")
	fmt.Println("在没有Redis连接的情况下，过滤管道将默认让所有商品通过")

	// 创建过滤管道（使用新的动态参数机制）
	filterPipeline := pipelines.NewFilterPipeline(120, map[string]interface{}{})

	// 打开管道
	if err := filterPipeline.Open(); err != nil {
		log.Fatalf("打开过滤管道失败: %v", err)
	}
	defer filterPipeline.Close()

	// 创建测试商品（包含Metadata）
	product := &models.ProductItem{
		ProductID: "DEMO001",
		Title:     "Demo Product",
		URL:       "https://example.com/demo001",
		Platform:  "demo",
		Price:     99.99,
		Currency:  "USD",
		InStock:   true,
		Country:   "US",
		SiteURL:   "https://example.com",
		CrawledAt: time.Now(),
		Metadata: map[string]interface{}{
			"filter": "demo_channel",
		},
	}

	// 处理商品
	ctx := context.Background()
	result, err := filterPipeline.ProcessItem(ctx, product)

	if err != nil {
		fmt.Printf("❌ 处理错误: %v\n", err)
	} else if result != nil {
		fmt.Printf("✅ 商品通过过滤器（默认行为）\n")
	}

	// 显示统计信息
	fmt.Println("\n统计信息:")
	stats := filterPipeline.GetStats()
	for key, value := range stats {
		fmt.Printf("%s: %v\n", key, value)
	}
}
