package config

import (
	"go-monitor/internal/types"
)

// ConfigProvider 纯配置访问提供者 - 专注于配置数据访问
type ConfigProvider struct {
	config *Config
}

// NewConfigProvider 创建配置提供者
func NewConfigProvider(config *Config) *ConfigProvider {
	return &ConfigProvider{
		config: config,
	}
}

// 注释：移除了 NewConfigProviderWithRawData，实现单一数据源

// App 获取应用配置
func (p *ConfigProvider) App() *AppConfig {
	if p.config == nil {
		return &AppConfig{}
	}
	return &p.config.App
}

// Database 获取数据库配置
func (p *ConfigProvider) Database() *DatabaseConfig {
	if p.config == nil {
		return &DatabaseConfig{}
	}
	return &p.config.Database
}

// Redis 获取Redis配置
func (p *ConfigProvider) Redis() *RedisConfig {
	if p.config == nil {
		return &RedisConfig{}
	}
	return &p.config.Redis
}

// RabbitMQ 获取RabbitMQ配置
func (p *ConfigProvider) RabbitMQ() *RabbitMQConfig {
	if p.config == nil {
		return &RabbitMQConfig{}
	}
	return &p.config.RabbitMQ
}

// 注释：移除了 HTTPClient 方法，因为 HTTPClientConfig 无用且未被实际使用

// Logging 获取日志配置
func (p *ConfigProvider) Logging() *LoggingConfig {
	if p.config == nil {
		return &LoggingConfig{}
	}
	return &p.config.Logging
}

// Monitor 获取指定监控配置
func (p *ConfigProvider) Monitor(name string) *types.SpiderConfig {
	if p.config == nil || p.config.Monitors == nil {
		return &types.SpiderConfig{}
	}
	if config, exists := p.config.Monitors[name]; exists {
		return config
	}
	return &types.SpiderConfig{}
}

// Monitors 获取所有监控配置
func (p *ConfigProvider) Monitors() map[string]*types.SpiderConfig {
	if p.config == nil {
		return make(map[string]*types.SpiderConfig)
	}
	return p.config.Monitors
}

// Notification 获取指定通知配置
func (p *ConfigProvider) Notification(name string) *NotificationConfig {
	if p.config == nil || p.config.Notifications == nil {
		return &NotificationConfig{}
	}
	if config, exists := p.config.Notifications[name]; exists {
		return &config
	}
	return &NotificationConfig{}
}

// Notifications 获取所有通知配置
func (p *ConfigProvider) Notifications() map[string]NotificationConfig {
	if p.config == nil {
		return make(map[string]NotificationConfig)
	}
	return p.config.Notifications
}

// Middlewares 获取中间件配置
func (p *ConfigProvider) Middlewares() *MiddlewaresConfig {
	if p.config == nil {
		return &MiddlewaresConfig{}
	}
	return &p.config.Middlewares
}

// Pipelines 获取管道配置
func (p *ConfigProvider) Pipelines() *PipelinesConfig {
	if p.config == nil {
		return &PipelinesConfig{}
	}
	return &p.config.Pipelines
}

// Resource 获取资源配置
func (p *ConfigProvider) Resource() *ResourceConfig {
	if p.config == nil {
		return &ResourceConfig{}
	}
	return &p.config.Resource
}

// 注释：移除了 Debug 方法，DebugConfig 类型不存在

// GetSpiderConfig 从监控配置转换为爬虫配置 - 开箱即用
func (p *ConfigProvider) GetSpiderConfig(monitorName string) *types.SpiderConfig {
	monitor := p.Monitor(monitorName)
	if monitor == nil {
		return &types.SpiderConfig{}
	}

	// 直接返回配置，开箱即用
	return monitor
}

// GetAllSpiderConfigs 获取所有爬虫配置 - 开箱即用
func (p *ConfigProvider) GetAllSpiderConfigs() map[string]*types.SpiderConfig {
	result := make(map[string]*types.SpiderConfig)

	for name := range p.Monitors() {
		result[name] = p.GetSpiderConfig(name)
	}

	return result
}

// GetAllSpiderConfigsForManager 获取所有爬虫配置 - 开箱即用，直接返回类型化配置
func (p *ConfigProvider) GetAllSpiderConfigsForManager() map[string]*types.SpiderConfig {
	result := make(map[string]*types.SpiderConfig)

	monitors := p.Monitors()
	for name, monitorConfig := range monitors {
		// 设置默认值
		if monitorConfig.SpiderSettings == nil {
			monitorConfig.SpiderSettings = make(map[string]interface{})
		}

		// 从spider_type设置Platform字段（如果Platform为空）
		if monitorConfig.Platform == "" {
			if spiderType, ok := monitorConfig.SpiderSettings["spider_type"].(string); ok {
				monitorConfig.Platform = spiderType
			}
		}

		// 直接使用配置，开箱即用
		result[name] = monitorConfig
	}

	return result
}

// 注释：PipelineConfig 现在在 types.go 中定义

// GetPipelineConfigs 获取管道配置列表 - 直接从结构化配置读取
func (p *ConfigProvider) GetPipelineConfigs() map[string]PipelineConfig {
	result := make(map[string]PipelineConfig)
	pipelines := p.Pipelines()
	if pipelines == nil {
		return result
	}

	// 直接从结构化配置中读取
	if pipelines.Validation.Enabled {
		result["ValidationPipeline"] = pipelines.Validation
	}
	if pipelines.Duplicates.Enabled {
		result["DuplicatesPipeline"] = pipelines.Duplicates
	}
	if pipelines.Filter.Enabled {
		result["FilterPipeline"] = pipelines.Filter
	}
	if pipelines.Notification.Enabled {
		result["NotificationPipeline"] = pipelines.Notification
	}

	return result
}

// 注释：MiddlewareConfig 现在在 types.go 中定义

// GetMiddlewareConfigs 获取中间件配置列表 - 直接从结构化配置读取
func (p *ConfigProvider) GetMiddlewareConfigs() map[string]MiddlewareConfig {
	result := make(map[string]MiddlewareConfig)
	middlewares := p.Middlewares()
	if middlewares == nil {
		return result
	}

	// 直接从结构化配置中读取
	if middlewares.Proxy.Enabled {
		result["ProxyMiddleware"] = middlewares.Proxy
	}
	if middlewares.Headers.Enabled {
		result["HeadersMiddleware"] = middlewares.Headers
	}
	if middlewares.Retry.Enabled {
		result["RetryMiddleware"] = middlewares.Retry
	}
	if middlewares.Cookies.Enabled {
		result["CookiesMiddleware"] = middlewares.Cookies
	}
	if middlewares.Signature.Enabled {
		result["SignatureMiddleware"] = middlewares.Signature
	}
	if middlewares.TLSFingerprint.Enabled {
		result["tls_fingerprint"] = middlewares.TLSFingerprint
	}

	return result
}

// GetResourceManagerConfig 获取资源管理器配置 - 开箱即用
// 直接返回配置数据，让调用者进行最终的类型转换
func (p *ConfigProvider) GetResourceManagerConfig() (map[string]interface{}, map[string]map[string]interface{}) {
	resourceConfig := p.Resource()
	if resourceConfig == nil {
		return nil, nil
	}

	// 返回全局配置
	globalConfig := map[string]interface{}{
		"enabled":            resourceConfig.Global.Enabled,
		"refresh_interval":   resourceConfig.Global.RefreshInterval,
		"max_retry_attempts": resourceConfig.Global.MaxRetryAttempts,
		"retry_delay":        resourceConfig.Global.RetryDelay,
	}

	// 返回初始化器配置
	initializersConfig := make(map[string]map[string]interface{})
	for name, initConfig := range resourceConfig.Initializers {
		initializersConfig[name] = map[string]interface{}{
			"enabled":      initConfig.Enabled,
			"priority":     initConfig.Priority,
			"dependencies": initConfig.Dependencies,
			"ttl":          initConfig.TTL,
			"config":       initConfig.Config,
		}
	}

	return globalConfig, initializersConfig
}
