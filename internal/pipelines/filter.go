package pipelines

import (
	"context"
	"crypto/md5"
	"encoding/json"
	"fmt"
	"strings"
	"sync"
	"time"

	"go-monitor/internal/models"
	"go-monitor/internal/services/redis"
)

// FilterConfig 过滤器配置结构
type FilterConfig struct {
	ChannelInfo struct {
		ChannelID   string    `json:"channel_id"`
		ChannelName string    `json:"channel_name"`
		GuildID     string    `json:"guild_id"`
		CreatedAt   time.Time `json:"created_at"`
		UpdatedAt   time.Time `json:"updated_at"`
	} `json:"channel_info"`
	FilterRules []FilterRule `json:"filter_rules"`
}

// FilterRule 过滤规则结构
type FilterRule struct {
	Keyword   string `json:"keyword"`
	Mode      string `json:"mode"` // "blacklist" 或 "whitelist"
	CreatedBy string `json:"created_by"`
}

// FilterPipeline 过滤管道 - 基于Redis配置的动态过滤器
type FilterPipeline struct {
	*BasePipeline

	// Redis连接（通过依赖注入）
	redisManager *redis.RedisManager

	// 配置缓存
	cachedConfig     *FilterConfig
	configHash       string
	lastConfigUpdate time.Time
	filterParam      string // 动态过滤器参数

	// 扩展统计信息
	stats struct {
		Processed     int64 `json:"processed"`
		Dropped       int64 `json:"dropped"`
		Errors        int64 `json:"errors"`
		Filtered      int64 `json:"filtered"`       // 被过滤掉的商品数量
		ConfigUpdates int64 `json:"config_updates"` // 配置更新次数
		RedisErrors   int64 `json:"redis_errors"`   // Redis访问错误次数
		DefaultPassed int64 `json:"default_passed"` // 默认通过的商品数量（配置不存在时）
	}

	// 自己的mutex
	statsMu  sync.RWMutex
	configMu sync.RWMutex
}

// NewFilterPipeline 创建过滤管道
func NewFilterPipeline(priority int, config map[string]interface{}) *FilterPipeline {
	base := NewBasePipeline("filter", priority, config)

	// 从配置中获取过滤器参数
	filterParam := "default"
	if param, ok := config["filter_param"].(string); ok && param != "" {
		filterParam = param
	}

	return &FilterPipeline{
		BasePipeline: base,
		filterParam:  filterParam,
	}
}

// SetRedisManager 设置Redis管理器（依赖注入）
func (f *FilterPipeline) SetRedisManager(redisManager *redis.RedisManager) {
	f.redisManager = redisManager
}

// Open 打开管道
func (f *FilterPipeline) Open() error {
	f.logger.Info(fmt.Sprintf("打开过滤管道：%s，过滤器参数：%s", f.name, f.filterParam))

	// 预加载配置
	if err := f.loadFilterConfig(); err != nil {
		f.logger.Warn(fmt.Sprintf("预加载过滤配置失败：%s，将使用默认通过模式", err.Error()))
	}

	return nil
}

// Close 关闭管道
func (f *FilterPipeline) Close() error {
	stats := f.GetStats()
	f.logger.Info(fmt.Sprintf("关闭过滤管道：%s，统计：%v", f.name, stats))

	// 调用基类的Close方法
	return f.BasePipeline.Close()
}

// ProcessItem 处理数据项，应用过滤规则
func (f *FilterPipeline) ProcessItem(ctx context.Context, item interface{}) (interface{}, error) {
	// 调用父类方法，增加处理计数
	f.updateStats("processed", 1)

	// 检查数据项类型
	var productItem *models.ProductItem
	switch it := item.(type) {
	case *models.ProductItem:
		productItem = it
	case models.ProductItem:
		productItem = &it
	default:
		f.updateStats("errors", 1)
		f.logger.Error(fmt.Sprintf("不支持的数据项类型：%T", item))
		return nil, fmt.Errorf("unsupported item type: %T", item)
	}

	// 获取当前过滤配置
	config, err := f.getFilterConfig()
	if err != nil {
		// Redis访问失败，记录错误但默认让商品通过
		f.updateStats("redis_errors", 1)
		f.updateStats("default_passed", 1)
		f.logger.Warn(fmt.Sprintf("获取过滤配置失败：%s，商品默认通过：%s",
			err.Error(), productItem.ProductID), "platform", productItem.Platform)
		return item, nil
	}

	if config == nil {
		// 配置不存在，默认让所有商品通过
		f.updateStats("default_passed", 1)
		f.logger.Debug(fmt.Sprintf("过滤配置不存在，商品默认通过：%s",
			productItem.ProductID), "platform", productItem.Platform)
		return item, nil
	}

	// 应用过滤规则
	shouldPass := f.applyFilterRules(productItem, config.FilterRules)

	if !shouldPass {
		// 商品被过滤掉
		f.updateStats("filtered", 1)
		f.logger.Debug(fmt.Sprintf("商品被过滤器拦截：%s，标题：%s",
			productItem.ProductID, productItem.Title), "platform", productItem.Platform)
		return nil, nil
	}

	// 商品通过过滤器
	f.logger.Debug(fmt.Sprintf("商品通过过滤器：%s",
		productItem.ProductID), "platform", productItem.Platform)
	return item, nil
}

// getFilterConfig 获取过滤配置，支持缓存和变更检测
func (f *FilterPipeline) getFilterConfig() (*FilterConfig, error) {
	f.configMu.Lock()
	defer f.configMu.Unlock()

	// 检查Redis连接是否可用
	if f.redisManager == nil {
		return nil, fmt.Errorf("redis manager not initialized")
	}

	// 构建Redis键
	redisKey := fmt.Sprintf("zeka:filter_rules:channel:%s", f.filterParam)

	ctx := context.Background()

	// 获取配置数据
	data, err := f.redisManager.Get(ctx, redisKey)
	if err != nil {
		if err == redis.ErrKeyNotFound {
			// 配置不存在，清空缓存并返回nil
			f.cachedConfig = nil
			f.configHash = ""
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get filter config from redis: %w", err)
	}

	// 计算数据哈希值
	newHash := fmt.Sprintf("%x", md5.Sum(data))

	// 检查配置是否有变化
	if f.configHash == newHash && f.cachedConfig != nil {
		// 配置未变化，返回缓存的配置
		return f.cachedConfig, nil
	}

	// 解析配置
	var config FilterConfig
	if err := json.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal filter config: %w", err)
	}

	// 更新缓存
	f.cachedConfig = &config
	f.configHash = newHash
	f.lastConfigUpdate = time.Now()
	f.updateStats("config_updates", 1)

	f.logger.Info(fmt.Sprintf("过滤配置已更新，规则数量：%d，更新时间：%s",
		len(config.FilterRules), config.ChannelInfo.UpdatedAt.Format("2006-01-02 15:04:05")))

	return &config, nil
}

// loadFilterConfig 预加载过滤配置
func (f *FilterPipeline) loadFilterConfig() error {
	_, err := f.getFilterConfig()
	return err
}

// applyFilterRules 应用过滤规则
func (f *FilterPipeline) applyFilterRules(item *models.ProductItem, rules []FilterRule) bool {
	if len(rules) == 0 {
		// 没有规则，默认通过
		return true
	}

	// 分离黑名单和白名单规则
	var blacklistRules []FilterRule
	var whitelistRules []FilterRule

	for _, rule := range rules {
		switch strings.ToLower(rule.Mode) {
		case "blacklist":
			blacklistRules = append(blacklistRules, rule)
		case "whitelist":
			whitelistRules = append(whitelistRules, rule)
		}
	}

	// 如果有白名单规则，先应用白名单过滤
	if len(whitelistRules) > 0 {
		whitelistPassed := false
		for _, rule := range whitelistRules {
			if f.matchesKeyword(item, rule.Keyword) {
				whitelistPassed = true
				break
			}
		}

		// 白名单检查失败，直接拒绝
		if !whitelistPassed {
			f.logger.Debug(fmt.Sprintf("商品未通过白名单检查：%s", item.ProductID))
			return false
		}
	}

	// 应用黑名单过滤
	for _, rule := range blacklistRules {
		if f.matchesKeyword(item, rule.Keyword) {
			f.logger.Debug(fmt.Sprintf("商品被黑名单规则拦截：%s，关键词：%s",
				item.ProductID, rule.Keyword))
			return false
		}
	}

	// 通过所有过滤规则
	return true
}

// matchesKeyword 检查商品是否匹配关键词
func (f *FilterPipeline) matchesKeyword(item *models.ProductItem, keyword string) bool {
	// 检查Title字段
	if strings.Contains(strings.ToLower(item.Title), strings.ToLower(keyword)) {
		return true
	}

	// 检查ProductID字段
	if strings.Contains(strings.ToLower(item.ProductID), strings.ToLower(keyword)) {
		return true
	}

	return false
}

// GetStats 获取扩展统计信息
func (f *FilterPipeline) GetStats() map[string]interface{} {
	f.statsMu.RLock()
	defer f.statsMu.RUnlock()

	return map[string]interface{}{
		"processed":      f.stats.Processed,
		"dropped":        f.stats.Dropped,
		"errors":         f.stats.Errors,
		"filtered":       f.stats.Filtered,
		"config_updates": f.stats.ConfigUpdates,
		"redis_errors":   f.stats.RedisErrors,
		"default_passed": f.stats.DefaultPassed,
	}
}

// updateStats 更新统计信息
func (f *FilterPipeline) updateStats(field string, value int64) {
	f.statsMu.Lock()
	defer f.statsMu.Unlock()

	switch field {
	case "processed":
		f.stats.Processed += value
	case "dropped":
		f.stats.Dropped += value
	case "errors":
		f.stats.Errors += value
	case "filtered":
		f.stats.Filtered += value
	case "config_updates":
		f.stats.ConfigUpdates += value
	case "redis_errors":
		f.stats.RedisErrors += value
	case "default_passed":
		f.stats.DefaultPassed += value
	}
}
