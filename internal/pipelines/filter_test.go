package pipelines

import (
	"context"
	"encoding/json"
	"fmt"
	"testing"
	"time"

	"go-monitor/internal/models"
	"go-monitor/internal/services/redis"

	"github.com/stretchr/testify/assert"
)

// RedisInterface 定义Redis接口用于测试
type RedisInterface interface {
	Get(ctx context.Context, key string) ([]byte, error)
}

// MockRedisManager 模拟Redis管理器
type MockRedisManager struct {
	data map[string][]byte
}

func NewMockRedisManager() *MockRedisManager {
	return &MockRedisManager{
		data: make(map[string][]byte),
	}
}

func (m *MockRedisManager) Get(ctx context.Context, key string) ([]byte, error) {
	if data, exists := m.data[key]; exists {
		return data, nil
	}
	return nil, redis.ErrKeyNotFound
}

func (m *MockRedisManager) Set(key string, data []byte) {
	m.data[key] = data
}

func (m *MockRedisManager) SetError(key string) {
	// 设置一个特殊标记来模拟错误
	m.data[key+"_error"] = []byte("error")
}

func (m *MockRedisManager) HasError(key string) bool {
	_, exists := m.data[key+"_error"]
	return exists
}

// 为了测试，我们需要修改FilterPipeline来支持接口注入
type TestableFilterPipeline struct {
	*FilterPipeline
	testRedis RedisInterface
}

func NewTestableFilterPipeline(priority int, config map[string]interface{}) *TestableFilterPipeline {
	base := NewFilterPipeline(priority, config)
	return &TestableFilterPipeline{
		FilterPipeline: base,
	}
}

func (t *TestableFilterPipeline) SetTestRedis(redis RedisInterface) {
	t.testRedis = redis
}

// ProcessItem 重写ProcessItem方法以使用测试Redis
func (t *TestableFilterPipeline) ProcessItem(ctx context.Context, item interface{}) (interface{}, error) {
	// 调用父类方法，增加处理计数
	t.updateStats("processed", 1)

	// 检查数据项类型
	var productItem *models.ProductItem
	switch it := item.(type) {
	case *models.ProductItem:
		productItem = it
	case models.ProductItem:
		productItem = &it
	default:
		t.updateStats("errors", 1)
		t.logger.Error(fmt.Sprintf("不支持的数据项类型：%T", item))
		return nil, fmt.Errorf("unsupported item type: %T", item)
	}

	// 从Metadata中提取filter参数
	filterValue, err := t.extractFilterFromMetadata(productItem)
	if err != nil {
		// Metadata解析错误，默认让商品通过
		t.updateStats("metadata_errors", 1)
		t.updateStats("default_passed", 1)
		t.logger.Debug(fmt.Sprintf("Metadata解析错误：%s，商品默认通过：%s",
			err.Error(), productItem.ProductID), "platform", productItem.Platform)
		return item, nil
	}

	if filterValue == "" {
		// 没有filter参数，默认让商品通过
		t.updateStats("missing_filter", 1)
		t.updateStats("default_passed", 1)
		t.logger.Debug(fmt.Sprintf("缺少filter参数，商品默认通过：%s",
			productItem.ProductID), "platform", productItem.Platform)
		return item, nil
	}

	// 获取当前过滤配置（使用测试方法）
	config, err := t.getTestFilterConfig(filterValue)
	if err != nil {
		// Redis访问失败，记录错误但默认让商品通过
		t.updateStats("redis_errors", 1)
		t.updateStats("default_passed", 1)
		t.logger.Warn(fmt.Sprintf("获取过滤配置失败：%s，商品默认通过：%s",
			err.Error(), productItem.ProductID), "platform", productItem.Platform)
		return item, nil
	}

	if config == nil {
		// 配置不存在，默认让所有商品通过
		t.updateStats("default_passed", 1)
		t.logger.Debug(fmt.Sprintf("过滤配置不存在（filter=%s），商品默认通过：%s",
			filterValue, productItem.ProductID), "platform", productItem.Platform)
		return item, nil
	}

	// 应用过滤规则
	shouldPass := t.applyFilterRules(productItem, config.FilterRules)

	if !shouldPass {
		// 商品被过滤掉
		t.updateStats("filtered", 1)
		t.logger.Debug(fmt.Sprintf("商品被过滤器拦截（filter=%s）：%s，标题：%s",
			filterValue, productItem.ProductID, productItem.Title), "platform", productItem.Platform)
		return nil, nil
	}

	// 商品通过过滤器
	t.logger.Debug(fmt.Sprintf("商品通过过滤器（filter=%s）：%s",
		filterValue, productItem.ProductID), "platform", productItem.Platform)
	return item, nil
}

// getTestFilterConfig 测试专用的配置获取方法
func (t *TestableFilterPipeline) getTestFilterConfig(filterValue string) (*FilterConfig, error) {
	t.configMu.Lock()
	defer t.configMu.Unlock()

	// 检查测试Redis连接是否可用
	if t.testRedis == nil {
		return nil, fmt.Errorf("redis manager not initialized")
	}

	// 构建Redis键
	redisKey := fmt.Sprintf("zeka:filter_rules:channel:%s", filterValue)

	ctx := context.Background()

	// 检查是否模拟错误
	if mockRedis, ok := t.testRedis.(*MockRedisManager); ok && mockRedis.HasError(redisKey) {
		return nil, fmt.Errorf("simulated redis error")
	}

	// 获取配置数据
	data, err := t.testRedis.Get(ctx, redisKey)
	if err != nil {
		if err == redis.ErrKeyNotFound {
			// 配置不存在，清空对应的缓存并返回nil
			delete(t.configCache, filterValue)
			delete(t.configHashCache, filterValue)
			delete(t.lastUpdateCache, filterValue)
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get filter config from redis: %w", err)
	}

	// 计算数据哈希值
	newHash := fmt.Sprintf("%x", data) // 简化哈希计算

	// 检查配置是否有变化
	if cachedHash, exists := t.configHashCache[filterValue]; exists && cachedHash == newHash {
		if cachedConfig, exists := t.configCache[filterValue]; exists {
			// 配置未变化，返回缓存的配置
			return cachedConfig, nil
		}
	}

	// 解析配置
	var config FilterConfig
	if err := json.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal filter config: %w", err)
	}

	// 更新缓存
	t.configCache[filterValue] = &config
	t.configHashCache[filterValue] = newHash
	t.lastUpdateCache[filterValue] = time.Now()
	t.updateStats("config_updates", 1)

	return &config, nil
}

// 创建测试用的ProductItem
func createTestProductItem(productID, title string) *models.ProductItem {
	return &models.ProductItem{
		ProductID: productID,
		Title:     title,
		URL:       "https://example.com/product/" + productID,
		Platform:  "test",
		Price:     99.99,
		Currency:  "USD",
		InStock:   true,
		Country:   "US",
		SiteURL:   "https://example.com",
		CrawledAt: time.Now(),
		Metadata: map[string]interface{}{
			"filter": "test", // 添加默认的filter参数
		},
	}
}

// 创建带有指定filter的测试ProductItem
func createTestProductItemWithFilter(productID, title, filter string) *models.ProductItem {
	item := createTestProductItem(productID, title)
	if filter == "" {
		// 如果filter为空，则不设置或设置为nil
		item.Metadata = nil
	} else {
		item.Metadata = map[string]interface{}{
			"filter": filter,
		}
	}
	return item
}

// 创建测试用的过滤配置
func createTestFilterConfig(rules []FilterRule) *FilterConfig {
	return &FilterConfig{
		ChannelInfo: struct {
			ChannelID   string    `json:"channel_id"`
			ChannelName string    `json:"channel_name"`
			GuildID     string    `json:"guild_id"`
			CreatedAt   time.Time `json:"created_at"`
			UpdatedAt   time.Time `json:"updated_at"`
		}{
			ChannelID:   "123456789",
			ChannelName: "test-channel",
			GuildID:     "987654321",
			CreatedAt:   time.Now().Add(-time.Hour),
			UpdatedAt:   time.Now(),
		},
		FilterRules: rules,
	}
}

func TestFilterPipeline_ProcessItem_NoConfig(t *testing.T) {
	// 创建可测试的过滤管道
	pipeline := NewTestableFilterPipeline(150, map[string]interface{}{
		"filter_param": "test",
	})

	// 创建模拟Redis管理器
	mockRedis := NewMockRedisManager()
	pipeline.SetTestRedis(mockRedis)

	// 不设置任何配置，模拟键不存在

	// 创建测试商品
	item := createTestProductItem("TEST001", "Test Product")

	// 处理商品
	result, err := pipeline.ProcessItem(context.Background(), item)

	// 验证结果
	assert.NoError(t, err)
	assert.Equal(t, item, result) // 配置不存在时应该通过

	// 验证统计信息
	stats := pipeline.GetStats()
	assert.Equal(t, int64(1), stats["processed"])
	assert.Equal(t, int64(1), stats["default_passed"])
}

func TestFilterPipeline_ProcessItem_BlacklistFilter(t *testing.T) {
	// 创建可测试的过滤管道
	pipeline := NewTestableFilterPipeline(150, map[string]interface{}{
		"filter_param": "test",
	})

	// 创建模拟Redis管理器
	mockRedis := NewMockRedisManager()
	pipeline.SetTestRedis(mockRedis)

	// 创建黑名单配置
	config := createTestFilterConfig([]FilterRule{
		{Keyword: "blocked", Mode: "blacklist", CreatedBy: "user1"},
		{Keyword: "spam", Mode: "blacklist", CreatedBy: "user1"},
	})
	configData, _ := json.Marshal(config)

	// 设置Redis返回配置
	mockRedis.Set("zeka:filter_rules:channel:test", configData)

	// 测试被黑名单拦截的商品
	blockedItem := createTestProductItem("TEST001", "This is a blocked product")
	result, err := pipeline.ProcessItem(context.Background(), blockedItem)

	assert.NoError(t, err)
	assert.Nil(t, result) // 应该被过滤掉

	// 测试通过黑名单的商品
	allowedItem := createTestProductItem("TEST002", "This is a normal product")
	result, err = pipeline.ProcessItem(context.Background(), allowedItem)

	assert.NoError(t, err)
	assert.Equal(t, allowedItem, result) // 应该通过

	// 验证统计信息
	stats := pipeline.GetStats()
	assert.Equal(t, int64(2), stats["processed"])
	assert.Equal(t, int64(1), stats["filtered"])
}

func TestFilterPipeline_ProcessItem_WhitelistFilter(t *testing.T) {
	// 创建可测试的过滤管道
	pipeline := NewTestableFilterPipeline(150, map[string]interface{}{
		"filter_param": "test",
	})

	// 创建模拟Redis管理器
	mockRedis := NewMockRedisManager()
	pipeline.SetTestRedis(mockRedis)

	// 创建白名单配置
	config := createTestFilterConfig([]FilterRule{
		{Keyword: "allowed", Mode: "whitelist", CreatedBy: "user1"},
		{Keyword: "premium", Mode: "whitelist", CreatedBy: "user1"},
	})
	configData, _ := json.Marshal(config)

	// 设置Redis返回配置
	mockRedis.Set("zeka:filter_rules:channel:test", configData)

	// 测试通过白名单的商品
	allowedItem := createTestProductItem("TEST001", "This is an allowed product")
	result, err := pipeline.ProcessItem(context.Background(), allowedItem)

	assert.NoError(t, err)
	assert.Equal(t, allowedItem, result) // 应该通过

	// 测试被白名单拦截的商品
	blockedItem := createTestProductItem("TEST002", "This is a normal product")
	result, err = pipeline.ProcessItem(context.Background(), blockedItem)

	assert.NoError(t, err)
	assert.Nil(t, result) // 应该被过滤掉

	// 验证统计信息
	stats := pipeline.GetStats()
	assert.Equal(t, int64(2), stats["processed"])
	assert.Equal(t, int64(1), stats["filtered"])
}

func TestFilterPipeline_ProcessItem_MixedFilter(t *testing.T) {
	// 创建可测试的过滤管道
	pipeline := NewTestableFilterPipeline(150, map[string]interface{}{
		"filter_param": "test",
	})

	// 创建模拟Redis管理器
	mockRedis := NewMockRedisManager()
	pipeline.SetTestRedis(mockRedis)

	// 创建混合配置（白名单+黑名单）
	config := createTestFilterConfig([]FilterRule{
		{Keyword: "premium", Mode: "whitelist", CreatedBy: "user1"},
		{Keyword: "blocked", Mode: "blacklist", CreatedBy: "user1"},
	})
	configData, _ := json.Marshal(config)

	// 设置Redis返回配置
	mockRedis.Set("zeka:filter_rules:channel:test", configData)

	// 测试通过白名单但被黑名单拦截的商品
	item1 := createTestProductItem("TEST001", "Premium blocked product")
	result, err := pipeline.ProcessItem(context.Background(), item1)

	assert.NoError(t, err)
	assert.Nil(t, result) // 应该被黑名单过滤掉

	// 测试通过白名单且不被黑名单拦截的商品
	item2 := createTestProductItem("TEST002", "Premium allowed product")
	result, err = pipeline.ProcessItem(context.Background(), item2)

	assert.NoError(t, err)
	assert.Equal(t, item2, result) // 应该通过

	// 测试不通过白名单的商品
	item3 := createTestProductItem("TEST003", "Normal product")
	result, err = pipeline.ProcessItem(context.Background(), item3)

	assert.NoError(t, err)
	assert.Nil(t, result) // 应该被白名单过滤掉
}

func TestFilterPipeline_ProcessItem_ProductIDMatching(t *testing.T) {
	// 创建可测试的过滤管道
	pipeline := NewTestableFilterPipeline(150, map[string]interface{}{
		"filter_param": "test",
	})

	// 创建模拟Redis管理器
	mockRedis := NewMockRedisManager()
	pipeline.SetTestRedis(mockRedis)

	// 创建基于ProductID的黑名单配置
	config := createTestFilterConfig([]FilterRule{
		{Keyword: "BLOCK", Mode: "blacklist", CreatedBy: "user1"},
	})
	configData, _ := json.Marshal(config)

	// 设置Redis返回配置
	mockRedis.Set("zeka:filter_rules:channel:test", configData)

	// 测试ProductID包含关键词的商品
	blockedItem := createTestProductItem("BLOCK123", "Normal product title")
	result, err := pipeline.ProcessItem(context.Background(), blockedItem)

	assert.NoError(t, err)
	assert.Nil(t, result) // 应该被过滤掉

	// 测试ProductID不包含关键词的商品
	allowedItem := createTestProductItem("ALLOW123", "Normal product title")
	result, err = pipeline.ProcessItem(context.Background(), allowedItem)

	assert.NoError(t, err)
	assert.Equal(t, allowedItem, result) // 应该通过
}

func TestFilterPipeline_ProcessItem_RedisError(t *testing.T) {
	// 创建可测试的过滤管道
	pipeline := NewTestableFilterPipeline(150, map[string]interface{}{
		"filter_param": "test",
	})

	// 创建模拟Redis管理器
	mockRedis := NewMockRedisManager()
	pipeline.SetTestRedis(mockRedis)

	// 模拟Redis连接错误
	mockRedis.SetError("zeka:filter_rules:channel:test")

	// 创建测试商品
	item := createTestProductItem("TEST001", "Test Product")

	// 处理商品
	result, err := pipeline.ProcessItem(context.Background(), item)

	// 验证结果
	assert.NoError(t, err)
	assert.Equal(t, item, result) // Redis错误时应该默认通过

	// 验证统计信息
	stats := pipeline.GetStats()
	assert.Equal(t, int64(1), stats["processed"])
	assert.Equal(t, int64(1), stats["redis_errors"])
	assert.Equal(t, int64(1), stats["default_passed"])
}

func TestFilterPipeline_ProcessItem_NoMetadata(t *testing.T) {
	// 创建可测试的过滤管道
	pipeline := NewTestableFilterPipeline(120, map[string]interface{}{})

	// 创建模拟Redis管理器
	mockRedis := NewMockRedisManager()
	pipeline.SetTestRedis(mockRedis)

	// 创建没有Metadata的测试商品
	item := createTestProductItemWithFilter("TEST001", "Test Product", "")

	// 处理商品
	result, err := pipeline.ProcessItem(context.Background(), item)

	// 验证结果
	assert.NoError(t, err)
	assert.Equal(t, item, result) // 没有Metadata时应该默认通过

	// 验证统计信息
	stats := pipeline.GetStats()
	assert.Equal(t, int64(1), stats["processed"])
	assert.Equal(t, int64(1), stats["missing_filter"])
	assert.Equal(t, int64(1), stats["default_passed"])
}

func TestFilterPipeline_ProcessItem_EmptyFilter(t *testing.T) {
	// 创建可测试的过滤管道
	pipeline := NewTestableFilterPipeline(120, map[string]interface{}{})

	// 创建模拟Redis管理器
	mockRedis := NewMockRedisManager()
	pipeline.SetTestRedis(mockRedis)

	// 创建filter为空字符串的测试商品
	item := createTestProductItem("TEST001", "Test Product")
	item.Metadata = map[string]interface{}{
		"filter": "",
	}

	// 处理商品
	result, err := pipeline.ProcessItem(context.Background(), item)

	// 验证结果
	assert.NoError(t, err)
	assert.Equal(t, item, result) // filter为空时应该默认通过

	// 验证统计信息
	stats := pipeline.GetStats()
	assert.Equal(t, int64(1), stats["processed"])
	assert.Equal(t, int64(1), stats["missing_filter"])
	assert.Equal(t, int64(1), stats["default_passed"])
}

func TestFilterPipeline_ProcessItem_DifferentFilters(t *testing.T) {
	// 创建可测试的过滤管道
	pipeline := NewTestableFilterPipeline(120, map[string]interface{}{})

	// 创建模拟Redis管理器
	mockRedis := NewMockRedisManager()
	pipeline.SetTestRedis(mockRedis)

	// 为不同的filter值设置不同的配置
	config1 := createTestFilterConfig([]FilterRule{
		{Keyword: "blocked", Mode: "blacklist", CreatedBy: "user1"},
	})
	configData1, _ := json.Marshal(config1)
	mockRedis.Set("zeka:filter_rules:channel:filter1", configData1)

	config2 := createTestFilterConfig([]FilterRule{
		{Keyword: "premium", Mode: "whitelist", CreatedBy: "user1"},
	})
	configData2, _ := json.Marshal(config2)
	mockRedis.Set("zeka:filter_rules:channel:filter2", configData2)

	// 测试filter1的商品（黑名单）
	item1 := createTestProductItemWithFilter("TEST001", "This is a blocked product", "filter1")
	result, err := pipeline.ProcessItem(context.Background(), item1)
	assert.NoError(t, err)
	assert.Nil(t, result) // 应该被黑名单过滤掉

	// 测试filter2的商品（白名单）
	item2 := createTestProductItemWithFilter("TEST002", "This is a premium product", "filter2")
	result, err = pipeline.ProcessItem(context.Background(), item2)
	assert.NoError(t, err)
	assert.Equal(t, item2, result) // 应该通过白名单

	// 测试filter2的普通商品（白名单）
	item3 := createTestProductItemWithFilter("TEST003", "This is a normal product", "filter2")
	result, err = pipeline.ProcessItem(context.Background(), item3)
	assert.NoError(t, err)
	assert.Nil(t, result) // 应该被白名单过滤掉

	// 验证统计信息
	stats := pipeline.GetStats()
	assert.Equal(t, int64(3), stats["processed"])
	assert.Equal(t, int64(2), stats["filtered"])
	assert.Equal(t, int64(2), stats["config_updates"]) // 两个不同的配置
}
