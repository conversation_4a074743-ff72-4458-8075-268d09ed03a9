package pipelines

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"go-monitor/internal/models"
	"go-monitor/internal/services/notification"
)

// NotificationMessage 通知消息结构
type NotificationMessage struct {
	ProductItem *models.ProductItem
	Groups      []string
	Platform    string
	Timestamp   time.Time
	Context     context.Context
}

// NotificationPipeline 通知管道 - 对应Python版本的notification.py
type NotificationPipeline struct {
	*BasePipeline

	// 配置驱动的设置
	notificationService *notification.Service

	// 消息队列 - 确保按时间顺序发送
	messageQueue chan *NotificationMessage
	queueWorker  context.Context
	queueCancel  context.CancelFunc
	queueWg      sync.WaitGroup

	// 扩展统计信息
	stats struct {
		Processed int64 `json:"processed"`
		Dropped   int64 `json:"dropped"`
		Errors    int64 `json:"errors"`
		Sent      int64 `json:"sent"`
		Skipped   int64 `json:"skipped"` // 新增：跳过的通知数
		Queued    int64 `json:"queued"`  // 新增：队列中的消息数
	}

	// 自己的mutex
	statsMu sync.RWMutex
}

// NewNotificationPipeline 创建通知管道
func NewNotificationPipeline(priority int, config map[string]interface{}) *NotificationPipeline {
	base := NewBasePipeline("notification", priority, config)

	// 直接使用传入的配置创建通知服务
	notificationService := notification.NewService(config)

	// 创建消息队列上下文
	queueCtx, queueCancel := context.WithCancel(context.Background())

	pipeline := &NotificationPipeline{
		BasePipeline:        base,
		notificationService: notificationService,
		messageQueue:        make(chan *NotificationMessage, 1000), // 缓冲1000条消息
		queueWorker:         queueCtx,
		queueCancel:         queueCancel,
	}

	// 启动消息队列处理器
	pipeline.startQueueWorker()

	// 记录初始状态
	configSource := "default"
	if len(config) > 0 {
		configSource = "provided"
	}

	// 简化创建日志，减少冗余信息
	pipeline.logger.Info(fmt.Sprintf("通知管道已创建，服务状态：%s，配置来源：%s，队列容量：1000",
		notificationService.GetServiceStatus().String(), configSource))

	return pipeline
}

// ProcessItem 处理数据项，生成通知 - 对应Python版本的process_item
func (n *NotificationPipeline) ProcessItem(ctx context.Context, item interface{}) (interface{}, error) {
	// 调用父类方法，增加处理计数
	n.updateStats("processed", 1)

	// 检查数据项类型
	var productItem *models.ProductItem

	switch it := item.(type) {
	case *models.ProductItem:
		productItem = it
	case models.ProductItem:
		productItem = &it
	default:
		n.updateStats("errors", 1)
		n.logger.Error(fmt.Sprintf("不支持的数据项类型：%T", item))
		return nil, fmt.Errorf("unsupported item type: %T", item)
	}

	platform := productItem.Platform
	if platform == "" {
		platform = "monitor"
	}

	// 检查库存
	if productItem.InStock {
		// 获取通知组
		groups := productItem.Notifications
		if len(groups) == 0 {
			groups = []string{"default"}
		}

		// 将消息放入队列，确保按时间顺序处理
		message := &NotificationMessage{
			ProductItem: productItem,
			Groups:      groups,
			Platform:    platform,
			Timestamp:   time.Now(),
			Context:     ctx,
		}

		select {
		case n.messageQueue <- message:
			n.updateStats("queued", 1)
		default:
			// 队列满了，记录错误但不阻塞
			n.updateStats("dropped", 1)
			n.logger.Warn(fmt.Sprintf("通知队列已满，丢弃消息 产品：%s", productItem.Title))
		}

		return productItem, nil
	} else {
		// 商品缺货，跳过通知（不记录日志以减少输出）
		n.updateStats("skipped", 1)
	}

	return nil, nil
}

// startQueueWorker 启动消息队列处理器
func (n *NotificationPipeline) startQueueWorker() {
	n.queueWg.Add(1)
	go func() {
		defer n.queueWg.Done()
		n.logger.Info("通知队列处理器已启动")

		for {
			select {
			case message := <-n.messageQueue:
				// 按顺序处理消息，确保时序正确
				n.processQueuedMessage(message)
			case <-n.queueWorker.Done():
				n.logger.Info("通知队列处理器正在停止")
				// 处理剩余消息
				for {
					select {
					case message := <-n.messageQueue:
						n.processQueuedMessage(message)
					default:
						n.logger.Info("通知队列处理器已停止")
						return
					}
				}
			}
		}
	}()
}

// processQueuedMessage 处理队列中的消息
func (n *NotificationPipeline) processQueuedMessage(message *NotificationMessage) {
	// 创建带超时的context，防止429无限等待
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
	defer cancel()

	// 记录处理开始时间
	startTime := time.Now()

	// 调用通知服务发送Discord通知
	result, err := n.notificationService.SendDiscordProductNotification(ctx, message.ProductItem, message.Groups, nil)

	// 计算处理时间
	processingTime := time.Since(startTime)

	if err != nil {
		n.updateStats("errors", 1)
		// 增强错误日志，包含处理时间和时序信息
		n.logger.Error(fmt.Sprintf("通知服务调用失败 URL：%s，组：%v，产品ID：%s，标题：%s，处理时间：%v，发现时间：%s，错误：%s",
			message.ProductItem.URL, message.Groups, message.ProductItem.ProductID, message.ProductItem.Title,
			processingTime, message.Timestamp.Format("15:04:05"), err.Error()),
			"platform", message.Platform)

		// 记录错误统计信息用于监控
		n.logNotificationFailure(message.ProductItem, message.Groups, err, "service_error")
	} else if result != nil && result.Success {
		n.updateStats("sent", 1)
		n.logger.Info(fmt.Sprintf("通知发送成功 产品：%s，组：%v，处理时间：%v，发现时间：%s",
			message.ProductItem.Title, message.Groups, processingTime, message.Timestamp.Format("15:04:05")),
			"platform", message.Platform)
	} else {
		n.updateStats("errors", 1)
		// 增强错误日志，提供更多诊断信息
		if result != nil {
			n.logger.Error(fmt.Sprintf("通知发送失败 URL：%s，组：%v，产品ID：%s，标题：%s，成功：%t，处理时间：%v，发现时间：%s，错误详情：%v",
				message.ProductItem.URL, message.Groups, message.ProductItem.ProductID, message.ProductItem.Title,
				result.Success, processingTime, message.Timestamp.Format("15:04:05"), result.Errors),
				"platform", message.Platform)

			// 分析错误类型并记录
			n.analyzeAndLogErrors(message.ProductItem, message.Groups, result.Errors)
		} else {
			n.logger.Error(fmt.Sprintf("通知发送失败 URL：%s，组：%v，产品ID：%s，标题：%s，处理时间：%v，发现时间：%s，结果：nil",
				message.ProductItem.URL, message.Groups, message.ProductItem.ProductID, message.ProductItem.Title,
				processingTime, message.Timestamp.Format("15:04:05")), "platform", message.Platform)

			n.logNotificationFailure(message.ProductItem, message.Groups, fmt.Errorf("通知结果为nil"), "null_result")
		}
	}
}

// Stop 停止通知管道
func (n *NotificationPipeline) Stop() {
	n.logger.Info("正在停止通知管道...")

	// 停止队列处理器
	n.queueCancel()

	// 等待队列处理器完成
	n.queueWg.Wait()

	// 关闭队列
	close(n.messageQueue)

	n.logger.Info("通知管道已停止")
}

// GetStats 获取统计信息 - 重写基类方法以包含扩展统计
func (n *NotificationPipeline) GetStats() map[string]interface{} {
	stats := n.BasePipeline.GetStats()

	n.statsMu.RLock()
	defer n.statsMu.RUnlock()

	// 添加通知特定的统计信息
	stats["sent"] = n.stats.Sent
	stats["skipped"] = n.stats.Skipped

	// 添加通知服务状态
	if n.notificationService != nil {
		serviceStats := n.notificationService.GetStats()
		stats["service"] = serviceStats
		stats["service_healthy"] = n.notificationService.IsHealthy()
		stats["service_status"] = n.notificationService.GetServiceStatus().String()
	}

	return stats
}

// updateStats 更新统计信息 - 重写基类方法以支持扩展统计
func (n *NotificationPipeline) updateStats(field string, value int64) {
	n.BasePipeline.updateStats(field, value)

	n.statsMu.Lock()
	defer n.statsMu.Unlock()

	switch field {
	case "sent":
		n.stats.Sent += value
	case "skipped":
		n.stats.Skipped += value
	}
}

// Close 关闭管道 - 对应Python版本的close
func (n *NotificationPipeline) Close() error {
	n.logger.Info(fmt.Sprintf("关闭通知管道，最终统计：%v", n.GetStats()))

	if n.notificationService != nil {
		err := n.notificationService.Close()
		if err != nil {
			n.logger.Error(fmt.Sprintf("关闭通知服务失败：%s", err.Error()))
		} else {
			n.logger.Info("通知服务已关闭")
		}
	}

	return n.BasePipeline.Close()
}

// GetNotificationService 获取通知服务实例（新增方法，用于测试或外部访问）
func (n *NotificationPipeline) GetNotificationService() *notification.Service {
	return n.notificationService
}

// logNotificationFailure 记录通知失败的详细信息用于监控和分析
func (n *NotificationPipeline) logNotificationFailure(productItem *models.ProductItem, groups []string, err error, errorType string) {
	// 记录结构化的错误信息，便于后续分析和监控
	n.logger.Warn(fmt.Sprintf("通知失败分析 类型：%s，产品：%s，组数量：%d，错误：%v",
		errorType, productItem.ProductID, len(groups), err))

	// 可以在这里添加更多的监控逻辑，比如发送到监控系统
	// 例如：metrics.IncrementCounter("notification_failures", map[string]string{"type": errorType})
}

// analyzeAndLogErrors 分析错误类型并记录详细信息
func (n *NotificationPipeline) analyzeAndLogErrors(productItem *models.ProductItem, groups []string, errors []string) {
	if len(errors) == 0 {
		return
	}

	// 分析错误模式
	networkErrors := 0
	rateLimitErrors := 0
	authErrors := 0
	otherErrors := 0

	for _, errMsg := range errors {
		if strings.Contains(errMsg, "timeout") || strings.Contains(errMsg, "connection") || strings.Contains(errMsg, "network") {
			networkErrors++
		} else if strings.Contains(errMsg, "429") || strings.Contains(errMsg, "rate limit") {
			rateLimitErrors++
		} else if strings.Contains(errMsg, "401") || strings.Contains(errMsg, "403") || strings.Contains(errMsg, "unauthorized") {
			authErrors++
		} else {
			otherErrors++
		}
	}

	// 记录错误分析结果
	n.logger.Warn(fmt.Sprintf("错误分析 产品：%s，网络错误：%d，速率限制：%d，认证错误：%d，其他错误：%d",
		productItem.ProductID, networkErrors, rateLimitErrors, authErrors, otherErrors))

	// 根据错误类型提供建议
	if networkErrors > 0 {
		n.logger.Info("建议：检查网络连接和DNS解析")
	}
	if rateLimitErrors > 0 {
		n.logger.Info("建议：降低通知频率或增加延迟")
	}
	if authErrors > 0 {
		n.logger.Info("建议：检查Discord Webhook URL的有效性")
	}
}
