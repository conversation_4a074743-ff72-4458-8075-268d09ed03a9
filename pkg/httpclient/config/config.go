// Package config 提供HTTP客户端配置管理
package config

import (
	"time"

	"go-monitor/pkg/httpclient/foundation"
)

// DefaultConfig 默认配置
var DefaultConfig = &foundation.Config{
	Timeout:         30 * time.Second,
	MaxIdleConns:    100,
	MaxConnsPerHost: 10,
	UserAgent:       "Go-Monitor-HTTPClient/1.0",

	// 功能开关
	EnableMonitoring:  false,
	EnableFingerprint: false,
	EnableRetry:       false,

	// 重试配置
	MaxRetries:   3,
	RetryDelay:   1 * time.Second,
	RetryBackoff: 2.0,

	// 指纹配置
	FingerprintConfig: nil,

	// 代理配置
	ProxyType: foundation.ProxyTypeNone,
	ProxyURL:  "",
	ProxyAuth: nil,

	// HTTP版本配置
	HTTPVersion: foundation.HTTPVersionHTTP2Preferred, // 优先使用HTTP/2

	// HTTP/2配置
	EnableHTTP2Push: true,
	HTTP2Settings: map[string]uint32{
		"MAX_CONCURRENT_STREAMS": 100,
		"INITIAL_WINDOW_SIZE":    65535,
	},
}

// GetDefault 获取默认配置的副本
func GetDefault() *foundation.Config {
	config := *DefaultConfig
	return &config
}

// NewBasicConfig 创建基础配置
func NewBasicConfig() *foundation.Config {
	return &foundation.Config{
		Timeout:         10 * time.Second,
		MaxIdleConns:    50,
		MaxConnsPerHost: 5,
		UserAgent:       "Go-Monitor-HTTPClient/1.0",

		EnableMonitoring:  false,
		EnableFingerprint: false,
		EnableRetry:       false,

		MaxRetries:   1,
		RetryDelay:   500 * time.Millisecond,
		RetryBackoff: 1.5,
	}
}

// NewAdvancedConfig 创建高级配置
func NewAdvancedConfig() *foundation.Config {
	return &foundation.Config{
		Timeout:         60 * time.Second,
		MaxIdleConns:    200,
		MaxConnsPerHost: 20,
		UserAgent:       "Go-Monitor-HTTPClient/1.0",

		EnableMonitoring:  true,
		EnableFingerprint: false,
		EnableRetry:       true,

		MaxRetries:   5,
		RetryDelay:   2 * time.Second,
		RetryBackoff: 2.0,
	}
}

// NewSpiderConfig 创建爬虫配置
func NewSpiderConfig() *foundation.Config {
	return &foundation.Config{
		Timeout:         45 * time.Second,
		MaxIdleConns:    150,
		MaxConnsPerHost: 15,
		UserAgent:       "Mozilla/5.0 (compatible; Go-Monitor-Spider/1.0)",

		EnableMonitoring:  true,
		EnableFingerprint: true,
		EnableRetry:       true,

		MaxRetries:   3,
		RetryDelay:   1 * time.Second,
		RetryBackoff: 1.8,

		FingerprintConfig: map[string]interface{}{
			"enabled": true,
			"pools":   []string{"default"},
		},

		// 代理配置
		ProxyType: foundation.ProxyTypeNone,
		ProxyURL:  "",
		ProxyAuth: nil,

		// HTTP版本配置
		HTTPVersion: foundation.HTTPVersionHTTP2Preferred, // 优先使用HTTP/2

		// HTTP/2配置
		EnableHTTP2Push: true,
		HTTP2Settings: map[string]uint32{
			"MAX_CONCURRENT_STREAMS": 100,
			"INITIAL_WINDOW_SIZE":    65535,
		},
	}
}

// NewHTTP2Config 创建HTTP/2优化配置
func NewHTTP2Config() *foundation.Config {
	config := GetDefault()
	config.HTTPVersion = foundation.HTTPVersionHTTP2Preferred
	config.EnableHTTP2Push = true
	config.MaxConnsPerHost = 5 // HTTP/2可以复用连接，减少连接数
	config.HTTP2Settings = map[string]uint32{
		"HEADER_TABLE_SIZE":      4096,
		"ENABLE_PUSH":            1,
		"MAX_CONCURRENT_STREAMS": 100,
		"INITIAL_WINDOW_SIZE":    65535,
		"MAX_FRAME_SIZE":         16384,
		"MAX_HEADER_LIST_SIZE":   8192,
	}
	return config
}

// NewProxyConfig 创建代理配置
func NewProxyConfig(proxyType foundation.ProxyType, proxyURL string, auth *foundation.ProxyAuth) *foundation.Config {
	config := GetDefault()
	config.ProxyType = proxyType
	config.ProxyURL = proxyURL
	config.ProxyAuth = auth
	return config
}

// NewSOCKS5Config 创建SOCKS5代理配置
func NewSOCKS5Config(proxyURL, username, password string) *foundation.Config {
	var auth *foundation.ProxyAuth
	if username != "" {
		auth = &foundation.ProxyAuth{
			Username: username,
			Password: password,
		}
	}
	return NewProxyConfig(foundation.ProxyTypeSOCKS5, proxyURL, auth)
}

// Validate 验证配置
func Validate(config *foundation.Config) error {
	if config == nil {
		return nil // 使用默认配置
	}

	// 验证超时时间
	if config.Timeout <= 0 {
		config.Timeout = DefaultConfig.Timeout
	}

	// 验证连接数
	if config.MaxIdleConns <= 0 {
		config.MaxIdleConns = DefaultConfig.MaxIdleConns
	}

	if config.MaxConnsPerHost <= 0 {
		config.MaxConnsPerHost = DefaultConfig.MaxConnsPerHost
	}

	// 验证User-Agent
	if config.UserAgent == "" {
		config.UserAgent = DefaultConfig.UserAgent
	}

	// 验证重试配置
	if config.EnableRetry {
		if config.MaxRetries <= 0 {
			config.MaxRetries = DefaultConfig.MaxRetries
		}

		if config.RetryDelay <= 0 {
			config.RetryDelay = DefaultConfig.RetryDelay
		}

		if config.RetryBackoff <= 1.0 {
			config.RetryBackoff = DefaultConfig.RetryBackoff
		}
	}

	return nil
}

// Merge 合并配置
func Merge(base, override *foundation.Config) *foundation.Config {
	if base == nil {
		base = GetDefault()
	}

	if override == nil {
		return base
	}

	// 创建新配置
	merged := *base

	// 覆盖非零值
	if override.Timeout > 0 {
		merged.Timeout = override.Timeout
	}

	if override.MaxIdleConns > 0 {
		merged.MaxIdleConns = override.MaxIdleConns
	}

	if override.MaxConnsPerHost > 0 {
		merged.MaxConnsPerHost = override.MaxConnsPerHost
	}

	if override.UserAgent != "" {
		merged.UserAgent = override.UserAgent
	}

	// 功能开关
	merged.EnableMonitoring = override.EnableMonitoring
	merged.EnableFingerprint = override.EnableFingerprint
	merged.EnableRetry = override.EnableRetry

	// 重试配置
	if override.MaxRetries > 0 {
		merged.MaxRetries = override.MaxRetries
	}

	if override.RetryDelay > 0 {
		merged.RetryDelay = override.RetryDelay
	}

	if override.RetryBackoff > 1.0 {
		merged.RetryBackoff = override.RetryBackoff
	}

	// 指纹配置
	if override.FingerprintConfig != nil {
		merged.FingerprintConfig = override.FingerprintConfig
	}

	return &merged
}

// LoadFromEnv 从环境变量加载配置
func LoadFromEnv() *foundation.Config {
	config := GetDefault()

	// 从环境变量读取配置
	if timeout := getEnvDuration("HTTPCLIENT_TIMEOUT"); timeout > 0 {
		config.Timeout = timeout
	}

	if maxIdleConns := getEnvInt("HTTPCLIENT_MAX_IDLE_CONNS"); maxIdleConns > 0 {
		config.MaxIdleConns = maxIdleConns
	}

	if maxConnsPerHost := getEnvInt("HTTPCLIENT_MAX_CONNS_PER_HOST"); maxConnsPerHost > 0 {
		config.MaxConnsPerHost = maxConnsPerHost
	}

	if userAgent := getEnvString("HTTPCLIENT_USER_AGENT"); userAgent != "" {
		config.UserAgent = userAgent
	}

	// 功能开关
	config.EnableMonitoring = getEnvBool("HTTPCLIENT_ENABLE_MONITORING", config.EnableMonitoring)
	config.EnableFingerprint = getEnvBool("HTTPCLIENT_ENABLE_FINGERPRINT", config.EnableFingerprint)
	config.EnableRetry = getEnvBool("HTTPCLIENT_ENABLE_RETRY", config.EnableRetry)

	// 重试配置
	if maxRetries := getEnvInt("HTTPCLIENT_MAX_RETRIES"); maxRetries > 0 {
		config.MaxRetries = maxRetries
	}

	if retryDelay := getEnvDuration("HTTPCLIENT_RETRY_DELAY"); retryDelay > 0 {
		config.RetryDelay = retryDelay
	}

	if retryBackoff := getEnvFloat("HTTPCLIENT_RETRY_BACKOFF"); retryBackoff > 1.0 {
		config.RetryBackoff = retryBackoff
	}

	return config
}
