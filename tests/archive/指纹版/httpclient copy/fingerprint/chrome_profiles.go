package fingerprint

import (
	"crypto/tls"
)

// GetChromeBasedProfiles 获取基于真实Chrome抓包的TLS指纹配置
func GetChromeBasedProfiles() []Profile {
	return []Profile{
		{
			Name:       "Chrome_Real_v1",
			MinVersion: tls.VersionTLS12,
			MaxVersion: tls.VersionTLS13,
			// 基于真实Chrome抓包的密码套件（移除GREASE，Go不支持）
			CipherSuites: []uint16{
				tls.TLS_AES_128_GCM_SHA256,
				tls.TLS_AES_256_GCM_SHA384,
				tls.TLS_CHACHA20_POLY1305_SHA256,
				tls.TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384,
				tls.TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256,
				tls.TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256,
				tls.TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,
				tls.TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,
				tls.TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256,
				tls.TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA,
				tls.TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA,
				tls.TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA,
				tls.TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA,
				tls.TLS_RSA_WITH_AES_256_GCM_SHA384,
				tls.TLS_RSA_WITH_AES_128_GCM_SHA256,
				tls.TLS_RSA_WITH_AES_256_CBC_SHA,
				tls.TLS_RSA_WITH_AES_128_CBC_SHA,
			},
			// 基于真实Chrome的曲线偏好（x25519优先）
			CurvePreferences: []tls.CurveID{tls.X25519, tls.CurveP256, tls.CurveP384, tls.CurveP521},
			SessionCache: SessionCacheStrategy{
				Enabled:          true,
				Size:             64,
				DisableOnBurst:   false,
				MaxCacheAge:      300,
				ClearProbability: 0.05,
			},
			// 关键：ALPN为空，匹配真实Chrome行为
			NextProtos: nil,
			ForceHTTP2: false,
		},
		{
			Name:       "Chrome_Real_v2",
			MinVersion: tls.VersionTLS12,
			MaxVersion: tls.VersionTLS13,
			// 轻微变体：调整密码套件顺序
			CipherSuites: []uint16{
				tls.TLS_AES_256_GCM_SHA384,
				tls.TLS_AES_128_GCM_SHA256,
				tls.TLS_CHACHA20_POLY1305_SHA256,
				tls.TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256,
				tls.TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,
				tls.TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384,
				tls.TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,
				tls.TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256,
				tls.TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256,
				tls.TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA,
				tls.TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA,
				tls.TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA,
				tls.TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA,
				tls.TLS_RSA_WITH_AES_128_GCM_SHA256,
				tls.TLS_RSA_WITH_AES_256_GCM_SHA384,
				tls.TLS_RSA_WITH_AES_128_CBC_SHA,
				tls.TLS_RSA_WITH_AES_256_CBC_SHA,
			},
			CurvePreferences: []tls.CurveID{tls.X25519, tls.CurveP256, tls.CurveP384},
			SessionCache: SessionCacheStrategy{
				Enabled:          true,
				Size:             48,
				DisableOnBurst:   false,
				MaxCacheAge:      240,
				ClearProbability: 0.08,
			},
			NextProtos: nil, // 保持ALPN为空
			ForceHTTP2: false,
		},
		{
			Name:       "Chrome_Real_v3",
			MinVersion: tls.VersionTLS13,
			MaxVersion: tls.VersionTLS13,
			// 纯TLS 1.3变体
			CipherSuites: []uint16{
				tls.TLS_AES_128_GCM_SHA256,
				tls.TLS_AES_256_GCM_SHA384,
				tls.TLS_CHACHA20_POLY1305_SHA256,
			},
			CurvePreferences: []tls.CurveID{tls.X25519, tls.CurveP256},
			SessionCache: SessionCacheStrategy{
				Enabled:          true,
				Size:             32,
				DisableOnBurst:   true,
				MaxCacheAge:      180,
				ClearProbability: 0.1,
			},
			NextProtos: nil, // ALPN为空
			ForceHTTP2: false,
		},
		{
			Name:       "Chrome_Incognito_v1",
			MinVersion: tls.VersionTLS12,
			MaxVersion: tls.VersionTLS13,
			// 隐私模式变体
			CipherSuites: []uint16{
				tls.TLS_AES_128_GCM_SHA256,
				tls.TLS_AES_256_GCM_SHA384,
				tls.TLS_CHACHA20_POLY1305_SHA256,
				tls.TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256,
				tls.TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,
				tls.TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384,
				tls.TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,
				tls.TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256,
				tls.TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256,
			},
			CurvePreferences: []tls.CurveID{tls.X25519, tls.CurveP256, tls.CurveP384},
			SessionCache: SessionCacheStrategy{
				Enabled:          false, // 隐私模式禁用会话缓存
				Size:             0,
				DisableOnBurst:   true,
				MaxCacheAge:      0,
				ClearProbability: 0.0,
			},
			NextProtos: nil, // ALPN为空
			ForceHTTP2: false,
		},
		{
			Name:       "Chrome_Mobile_v1",
			MinVersion: tls.VersionTLS12,
			MaxVersion: tls.VersionTLS13,
			// 移动端Chrome变体
			CipherSuites: []uint16{
				tls.TLS_AES_128_GCM_SHA256,
				tls.TLS_AES_256_GCM_SHA384,
				tls.TLS_CHACHA20_POLY1305_SHA256,
				tls.TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256,
				tls.TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,
				tls.TLS_ECDHE_ECDSA_WITH_CHACHA20_POLY1305_SHA256,
				tls.TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256,
				tls.TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384,
				tls.TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,
			},
			CurvePreferences: []tls.CurveID{tls.X25519, tls.CurveP256},
			SessionCache: SessionCacheStrategy{
				Enabled:          true,
				Size:             16, // 移动端较小缓存
				DisableOnBurst:   true,
				MaxCacheAge:      120,
				ClearProbability: 0.15,
			},
			NextProtos: nil, // ALPN为空
			ForceHTTP2: false,
		},
	}
}
